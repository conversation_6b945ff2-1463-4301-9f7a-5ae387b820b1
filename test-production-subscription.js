#!/usr/bin/env node

/**
 * Test Production Subscription Creation
 * 
 * This script simulates what the Flutter app is doing when creating a subscription
 * by making requests to the production backend.
 */

const axios = require('axios');

// Production configuration (same as Flutter app would use)
const PRODUCTION_BACKEND_URL = 'https://learner.netaapp.in'; // or whatever your production URL is
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'testpassword123';

console.log('🧪 Testing Production Subscription Creation');
console.log('=' .repeat(60));
console.log(`🌐 Production Backend: ${PRODUCTION_BACKEND_URL}`);

async function authenticateUser() {
  try {
    console.log('\n🔐 Step 1: Authenticating user...');
    
    // First, let's try to get the available plans without authentication
    const plansResponse = await axios.get(`${PRODUCTION_BACKEND_URL}/api/subscriptions/plans`, {
      headers: {
        'x-package-id': 'com.gumbo.learning'
      }
    });
    
    console.log('✅ Plans fetched successfully:');
    console.log('📋 Available plans:', JSON.stringify(plansResponse.data, null, 2));
    
    return plansResponse.data;
    
  } catch (error) {
    console.error('❌ Authentication/Plans fetch failed:');
    console.error('  Status:', error.response?.status);
    console.error('  Error:', error.response?.data);
    console.error('  Message:', error.message);
    throw error;
  }
}

async function createSubscription() {
  try {
    console.log('\n💳 Step 2: Creating subscription...');
    
    // Simulate what Flutter app sends for subscription creation
    const subscriptionData = {
      plan: 'monthly', // or 'yearly'
      recurring: true,
      phone: '9876543210'
    };
    
    console.log('📤 Sending subscription request:', subscriptionData);
    
    const response = await axios.post(
      `${PRODUCTION_BACKEND_URL}/api/subscriptions/create-order`,
      subscriptionData,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning',
          // Note: In real Flutter app, this would include authentication token
          // 'Authorization': 'Bearer <token>'
        }
      }
    );
    
    console.log('✅ Subscription created successfully:');
    console.log('📋 Response:', JSON.stringify(response.data, null, 2));
    
    return response.data;
    
  } catch (error) {
    console.error('❌ Subscription creation failed:');
    console.error('  Status:', error.response?.status);
    console.error('  Error:', JSON.stringify(error.response?.data, null, 2));
    console.error('  Message:', error.message);
    
    // Log the full error for debugging
    if (error.response?.data) {
      console.error('\n🔍 Detailed error analysis:');
      const errorData = error.response.data;
      
      if (errorData.message) {
        console.error('  Error Message:', errorData.message);
      }
      
      if (errorData.error) {
        console.error('  Error Details:', errorData.error);
      }
      
      if (errorData.stack) {
        console.error('  Stack Trace:', errorData.stack);
      }
    }
    
    throw error;
  }
}

async function testProductionFlow() {
  try {
    // Test 1: Get plans
    await authenticateUser();
    
    // Test 2: Create subscription (this will likely fail due to authentication)
    await createSubscription();
    
    console.log('\n✅ Production test completed successfully!');
    
  } catch (error) {
    console.log('\n❌ Production test failed');
    console.log('🔍 This helps us understand what the Flutter app is experiencing');
    
    // Provide debugging suggestions
    console.log('\n💡 Debugging suggestions:');
    console.log('1. Check if the production backend is running');
    console.log('2. Verify the production database connections');
    console.log('3. Check authentication requirements');
    console.log('4. Verify plan IDs are correctly configured in production');
    console.log('5. Check payment microservice integration in production');
  }
}

// Alternative: Test with a simpler endpoint first
async function testBasicConnectivity() {
  console.log('\n🔍 Testing basic connectivity to production backend...');
  
  try {
    const response = await axios.get(`${PRODUCTION_BACKEND_URL}/api/health`, {
      timeout: 10000
    });
    
    console.log('✅ Production backend is reachable');
    console.log('📋 Health check response:', response.data);
    
  } catch (error) {
    console.error('❌ Cannot reach production backend:');
    console.error('  Status:', error.response?.status);
    console.error('  Message:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('🔍 Connection refused - backend might be down');
    } else if (error.code === 'ENOTFOUND') {
      console.error('🔍 DNS resolution failed - check the URL');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('🔍 Request timed out - backend might be slow or unreachable');
    }
  }
}

// Run tests
if (require.main === module) {
  console.log('🚀 Starting production backend tests...\n');
  
  testBasicConnectivity()
    .then(() => testProductionFlow())
    .then(() => {
      console.log('\n🎯 Test completed!');
      process.exit(0);
    })
    .catch(() => {
      console.log('\n🎯 Test completed with errors (this is expected for debugging)');
      process.exit(0); // Don't fail the script, we want to see the errors
    });
}

module.exports = { testProductionFlow };
