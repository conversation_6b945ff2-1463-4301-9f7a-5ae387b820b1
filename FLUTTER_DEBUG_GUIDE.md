# 🐛 Flutter App Subscription Debug Guide

## 🔍 Issue Identified
The Flutter app is getting **500 "Failed to create subscription"** errors because of **authentication issues**.

## 🎯 Root Cause
- ✅ **Plans API works** (no auth required)
- ❌ **Subscription creation fails** (requires valid JWT token)
- 🔍 **Error**: "Token is not valid. User not found."

## 🔧 Flutter App Fixes Needed

### 1. **Check Authentication State**
```dart
// Before creating subscription, verify user is authenticated
if (!isUserAuthenticated()) {
  // Redirect to login or show authentication error
  return;
}
```

### 2. **Verify JWT Token in Requests**
```dart
// Ensure Authorization header is included in subscription requests
final headers = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer ${await getStoredToken()}',
  'x-package-id': 'com.gumbo.learning',
};
```

### 3. **Debug Token Storage**
```dart
// Add debug logging to check token
final token = await getStoredToken();
print('🔍 Debug: Token exists: ${token != null}');
print('🔍 Debug: Token length: ${token?.length ?? 0}');
// Don't log the actual token for security
```

### 4. **Handle Token Expiration**
```dart
// Check if token is expired before making requests
if (await isTokenExpired()) {
  await refreshToken(); // or redirect to login
}
```

### 5. **Add Error Handling**
```dart
try {
  final response = await createSubscription();
} catch (e) {
  if (e.toString().contains('401') || e.toString().contains('Token is not valid')) {
    // Handle authentication error
    await redirectToLogin();
  } else {
    // Handle other errors
    showErrorDialog('Subscription creation failed: ${e.toString()}');
  }
}
```

## 🧪 Testing Steps

### Step 1: Verify Authentication Flow
1. **Login with Google OAuth**
2. **Check if JWT token is stored** in secure storage
3. **Verify token format** (should be a long string with dots)

### Step 2: Test API Calls
1. **Test plans API** (should work without auth)
2. **Test user profile API** (should work with auth)
3. **Test subscription creation** (should work with valid auth)

### Step 3: Debug Network Requests
1. **Enable network logging** in Flutter
2. **Check if Authorization header** is being sent
3. **Verify token format** in requests

## 🔍 Debug Checklist

- [ ] User is authenticated before subscription attempt
- [ ] JWT token is stored in secure storage
- [ ] Authorization header is included in subscription requests
- [ ] Token is not expired
- [ ] User exists in backend database
- [ ] Network requests include proper headers
- [ ] Error handling for authentication failures

## 🚀 Quick Fix

**Add this debug code to your subscription creation method:**

```dart
Future<void> createSubscription() async {
  // Debug authentication state
  final token = await getStoredToken();
  print('🔍 Debug: Creating subscription...');
  print('🔍 Debug: Token exists: ${token != null}');
  
  if (token == null) {
    print('❌ Debug: No token found - redirecting to login');
    await redirectToLogin();
    return;
  }
  
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/api/subscriptions/create-order'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
        'x-package-id': packageId,
      },
      body: jsonEncode(subscriptionData),
    );
    
    print('🔍 Debug: Response status: ${response.statusCode}');
    print('🔍 Debug: Response body: ${response.body}');
    
    if (response.statusCode == 401) {
      print('❌ Debug: Authentication failed - token invalid');
      await redirectToLogin();
      return;
    }
    
    if (response.statusCode == 500) {
      print('❌ Debug: Server error - check backend logs');
      // Handle server error
      return;
    }
    
    // Handle success
    final data = jsonDecode(response.body);
    print('✅ Debug: Subscription created successfully');
    
  } catch (e) {
    print('❌ Debug: Exception: $e');
  }
}
```

## 📋 Expected Behavior After Fix

1. **✅ User authentication** should work properly
2. **✅ JWT tokens** should be included in requests
3. **✅ Subscription creation** should succeed
4. **✅ Error handling** should guide users appropriately

## 🔗 Backend Status

- ✅ **Backend APIs are working** correctly
- ✅ **Plan IDs are configured** properly
- ✅ **Payment microservice integration** is functional
- ✅ **Database connections** are working
- ✅ **Authentication middleware** is working

The issue is **purely on the Flutter app side** - specifically with JWT token handling in subscription requests.
