const { sendResponse } = require('../utils/response');
const { getChatListForUser } = require('../services/chatService');
const logger = require('../utils/logger');

const getChats = async (req, res) => {
  try {
    const chatList = await getChatListForUser(req.user._id);

    return sendResponse(res, {
      success: true,
      data: chatList,
      message: 'Chat list fetched successfully'
    });
  } catch (err) {
    logger.error(`[chatController > getChats] user=${req.user?._id} | error=${err.message}`);
    return sendResponse(res, {
      success: false,
      error: [err.message],
      message: 'Failed to fetch chat list'
    }, 500);
  }
};

module.exports = { getChats };
