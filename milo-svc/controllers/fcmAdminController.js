/**
 * FCM Admin Controller
 * 
 * This controller handles admin-level FCM operations including:
 * - Broadcast notifications
 * - Topic management
 * - Notification analytics
 * - Token cleanup
 * 
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

const User = require('../models/user');
const fcmService = require('../services/fcmService');
const notificationTemplates = require('../services/notificationTemplates');
const { sendResponse } = require('../utils/response');
const Logger = require('../utils/logger');

/**
 * Send broadcast notification to all users
 * POST /fcm/admin/broadcast
 */
exports.sendBroadcast = async (req, res) => {
  try {
    const { title, body, data = {}, notificationType = 'system_update', targetUsers } = req.body;

    if (!title || !body) {
      return sendResponse(res, {
        success: false,
        error: ['Title and body are required'],
        message: 'Invalid request data'
      }, 400);
    }

    let userIds;
    
    if (targetUsers && Array.isArray(targetUsers) && targetUsers.length > 0) {
      // Send to specific users
      userIds = targetUsers;
    } else {
      // Send to all users with FCM tokens
      const users = await User.find({
        'fcmTokens.0': { $exists: true },
        'fcmTokens.isActive': true
      }).select('_id');
      userIds = users.map(user => user._id.toString());
    }

    if (userIds.length === 0) {
      return sendResponse(res, {
        success: false,
        error: ['No users found with active FCM tokens'],
        message: 'No recipients available'
      }, 400);
    }

    const notification = { title, body };
    const result = await fcmService.sendToUsers(userIds, notification, data, {
      notificationType
    });

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to send broadcast notification'
      }, 500);
    }

    Logger.info('Broadcast notification sent', {
      adminId: req.user._id,
      totalUsers: result.totalUsers,
      totalSent: result.totalSent,
      notificationType
    });

    return sendResponse(res, {
      success: true,
      data: {
        totalUsers: result.totalUsers,
        totalSent: result.totalSent,
        successRate: ((result.totalSent / result.totalUsers) * 100).toFixed(2) + '%',
        message: 'Broadcast notification sent successfully'
      },
      message: 'Broadcast sent'
    });

  } catch (error) {
    Logger.error('Failed to send broadcast notification', {
      adminId: req.user?._id,
      error: error.message,
      stack: error.stack
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to send broadcast'
    }, 500);
  }
};

/**
 * Send topic notification
 * POST /fcm/admin/topic
 */
exports.sendTopicNotification = async (req, res) => {
  try {
    const { topic, title, body, data = {}, notificationType = 'system_update' } = req.body;

    if (!topic || !title || !body) {
      return sendResponse(res, {
        success: false,
        error: ['Topic, title, and body are required'],
        message: 'Invalid request data'
      }, 400);
    }

    // Validate topic name
    if (!/^[a-zA-Z0-9_]+$/.test(topic)) {
      return sendResponse(res, {
        success: false,
        error: ['Invalid topic name. Use only alphanumeric characters and underscores'],
        message: 'Invalid topic name'
      }, 400);
    }

    const notification = { title, body };
    const result = await fcmService.sendToTopic(topic, notification, data, {
      notificationType
    });

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to send topic notification'
      }, 500);
    }

    Logger.info('Topic notification sent', {
      adminId: req.user._id,
      topic,
      messageId: result.messageId,
      notificationType
    });

    return sendResponse(res, {
      success: true,
      data: {
        topic,
        messageId: result.messageId,
        message: 'Topic notification sent successfully'
      },
      message: 'Topic notification sent'
    });

  } catch (error) {
    Logger.error('Failed to send topic notification', {
      adminId: req.user?._id,
      topic: req.body?.topic,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to send topic notification'
    }, 500);
  }
};

/**
 * Get FCM statistics
 * GET /fcm/admin/stats
 */
exports.getStatistics = async (req, res) => {
  try {
    const result = await fcmService.getStatistics();

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to get statistics'
      }, 500);
    }

    return sendResponse(res, {
      success: true,
      data: result.statistics,
      message: 'Statistics retrieved successfully'
    });

  } catch (error) {
    Logger.error('Failed to get FCM statistics', {
      adminId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to get statistics'
    }, 500);
  }
};

/**
 * Clean up inactive FCM tokens
 * POST /fcm/admin/cleanup
 */
exports.cleanupTokens = async (req, res) => {
  try {
    const result = await fcmService.cleanupInactiveTokens();

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to cleanup tokens'
      }, 500);
    }

    Logger.info('FCM tokens cleanup completed', {
      adminId: req.user._id,
      modifiedCount: result.modifiedCount
    });

    return sendResponse(res, {
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        message: 'Token cleanup completed successfully'
      },
      message: 'Cleanup completed'
    });

  } catch (error) {
    Logger.error('Failed to cleanup FCM tokens', {
      adminId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to cleanup tokens'
    }, 500);
  }
};

/**
 * Get users with FCM tokens
 * GET /fcm/admin/users
 */
exports.getUsersWithTokens = async (req, res) => {
  try {
    const { page = 1, limit = 50, platform, active } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    const query = { 'fcmTokens.0': { $exists: true } };

    if (platform) {
      query['fcmTokens.platform'] = platform;
    }

    if (active !== undefined) {
      query['fcmTokens.isActive'] = active === 'true';
    }

    const [users, totalCount] = await Promise.all([
      User.find(query)
        .select('nickname email phone fcmTokens createdAt lastLogin')
        .skip(skip)
        .limit(parseInt(limit))
        .sort({ lastLogin: -1 }),
      User.countDocuments(query)
    ]);

    // Process user data
    const processedUsers = users.map(user => ({
      _id: user._id,
      nickname: user.nickname,
      email: user.email,
      phone: user.phone,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      tokens: user.fcmTokens.map(token => ({
        deviceId: token.deviceId,
        platform: token.platform,
        appVersion: token.appVersion,
        isActive: token.isActive,
        lastUsed: token.lastUsed,
        createdAt: token.createdAt
      })),
      totalTokens: user.fcmTokens.length,
      activeTokens: user.fcmTokens.filter(t => t.isActive).length
    }));

    return sendResponse(res, {
      success: true,
      data: {
        users: processedUsers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNext: skip + users.length < totalCount,
          hasPrev: page > 1
        }
      },
      message: 'Users retrieved successfully'
    });

  } catch (error) {
    Logger.error('Failed to get users with FCM tokens', {
      adminId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to get users'
    }, 500);
  }
};

/**
 * Send notification using template
 * POST /fcm/admin/template
 */
exports.sendTemplateNotification = async (req, res) => {
  try {
    const {
      templateType,
      templateData,
      targetUsers,
      language = 'en'
    } = req.body;

    if (!templateType || !templateData) {
      return sendResponse(res, {
        success: false,
        error: ['Template type and data are required'],
        message: 'Invalid request data'
      }, 400);
    }

    // Validate template type
    const validTemplates = [
      'new_message',
      'new_match',
      'super_message',
      'subscription_expiry',
      'promotion',
      'system_update'
    ];

    if (!validTemplates.includes(templateType)) {
      return sendResponse(res, {
        success: false,
        error: [`Invalid template type. Valid types: ${validTemplates.join(', ')}`],
        message: 'Invalid template type'
      }, 400);
    }

    // Get template
    let template;
    switch (templateType) {
      case 'new_message':
        template = notificationTemplates.getNewMessageTemplate(templateData, language);
        break;
      case 'new_match':
        template = notificationTemplates.getNewMatchTemplate(templateData, language);
        break;
      case 'super_message':
        template = notificationTemplates.getSuperMessageTemplate(templateData, language);
        break;
      case 'subscription_expiry':
        template = notificationTemplates.getSubscriptionExpiryTemplate(templateData, language);
        break;
      case 'promotion':
        template = notificationTemplates.getPromotionTemplate(templateData, language);
        break;
      case 'system_update':
        template = notificationTemplates.getSystemUpdateTemplate(templateData, language);
        break;
      default:
        template = notificationTemplates.getCustomTemplate(templateData, language);
    }

    let userIds;
    if (targetUsers && Array.isArray(targetUsers) && targetUsers.length > 0) {
      userIds = targetUsers;
    } else {
      // Send to all users with FCM tokens
      const users = await User.find({
        'fcmTokens.0': { $exists: true },
        'fcmTokens.isActive': true
      }).select('_id');
      userIds = users.map(user => user._id.toString());
    }

    if (userIds.length === 0) {
      return sendResponse(res, {
        success: false,
        error: ['No users found with active FCM tokens'],
        message: 'No recipients available'
      }, 400);
    }

    const result = await fcmService.sendToUsers(
      userIds,
      template.notification,
      template.data,
      template.options
    );

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to send template notification'
      }, 500);
    }

    Logger.info('Template notification sent', {
      adminId: req.user._id,
      templateType,
      language,
      totalUsers: result.totalUsers,
      totalSent: result.totalSent
    });

    return sendResponse(res, {
      success: true,
      data: {
        templateType,
        language,
        totalUsers: result.totalUsers,
        totalSent: result.totalSent,
        successRate: ((result.totalSent / result.totalUsers) * 100).toFixed(2) + '%',
        message: 'Template notification sent successfully'
      },
      message: 'Template notification sent'
    });

  } catch (error) {
    Logger.error('Failed to send template notification', {
      adminId: req.user?._id,
      templateType: req.body?.templateType,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to send template notification'
    }, 500);
  }
};
