/**
 * FCM (Firebase Cloud Messaging) Controller
 * 
 * This controller handles all FCM-related operations including:
 * - FCM token registration and management
 * - Notification preferences
 * - Push notification sending
 * - Topic subscription management
 * 
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

const User = require('../models/user');
const fcmService = require('../services/fcmService');
const notificationTemplates = require('../services/notificationTemplates');
const { sendResponse } = require('../utils/response');
const Logger = require('../utils/logger');

/**
 * Register FCM token for a user
 * POST /fcm/token/register
 */
exports.registerToken = async (req, res) => {
  try {
    const userId = req.user._id;
    const { token, deviceId, platform, appVersion } = req.body;

    // Validate required fields
    if (!token || !deviceId || !platform) {
      return sendResponse(res, {
        success: false,
        error: ['Token, deviceId, and platform are required'],
        message: 'Invalid request data'
      }, 400);
    }

    // Validate platform
    if (!['ios', 'android', 'web'].includes(platform)) {
      return sendResponse(res, {
        success: false,
        error: ['Platform must be ios, android, or web'],
        message: 'Invalid platform'
      }, 400);
    }

    // Validate FCM token format (basic validation)
    if (token.length < 50) {
      return sendResponse(res, {
        success: false,
        error: ['Invalid FCM token format'],
        message: 'Invalid token'
      }, 400);
    }

    // Validate token with FCM service
    const isValidToken = await fcmService.validateToken(token);
    if (!isValidToken) {
      return sendResponse(res, {
        success: false,
        error: ['Invalid or expired FCM token'],
        message: 'Token validation failed'
      }, 400);
    }

    // Add token to user
    const user = await User.findById(userId);
    if (!user) {
      return sendResponse(res, {
        success: false,
        error: ['User not found'],
        message: 'User not found'
      }, 404);
    }

    await user.addFCMToken({
      token,
      deviceId,
      platform,
      appVersion
    });

    Logger.info('FCM token registered successfully', {
      userId,
      deviceId,
      platform,
      tokenPreview: token.substring(0, 20) + '...'
    });

    return sendResponse(res, {
      success: true,
      data: {
        message: 'FCM token registered successfully',
        deviceId,
        platform
      },
      message: 'Token registered'
    });

  } catch (error) {
    Logger.error('Failed to register FCM token', {
      userId: req.user?._id,
      error: error.message,
      stack: error.stack
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to register token'
    }, 500);
  }
};

/**
 * Update FCM token activity
 * PUT /fcm/token/activity
 */
exports.updateTokenActivity = async (req, res) => {
  try {
    const userId = req.user._id;
    const { token } = req.body;

    if (!token) {
      return sendResponse(res, {
        success: false,
        error: ['Token is required'],
        message: 'Invalid request data'
      }, 400);
    }

    const user = await User.findById(userId);
    if (!user) {
      return sendResponse(res, {
        success: false,
        error: ['User not found'],
        message: 'User not found'
      }, 404);
    }

    await user.updateFCMTokenActivity(token);

    return sendResponse(res, {
      success: true,
      data: { message: 'Token activity updated' },
      message: 'Activity updated'
    });

  } catch (error) {
    Logger.error('Failed to update token activity', {
      userId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to update activity'
    }, 500);
  }
};

/**
 * Remove FCM token
 * DELETE /fcm/token/:deviceId
 */
exports.removeToken = async (req, res) => {
  try {
    const userId = req.user._id;
    const { deviceId } = req.params;

    if (!deviceId) {
      return sendResponse(res, {
        success: false,
        error: ['Device ID is required'],
        message: 'Invalid request data'
      }, 400);
    }

    const user = await User.findById(userId);
    if (!user) {
      return sendResponse(res, {
        success: false,
        error: ['User not found'],
        message: 'User not found'
      }, 404);
    }

    await user.removeFCMToken(deviceId);

    Logger.info('FCM token removed successfully', {
      userId,
      deviceId
    });

    return sendResponse(res, {
      success: true,
      data: { message: 'FCM token removed successfully' },
      message: 'Token removed'
    });

  } catch (error) {
    Logger.error('Failed to remove FCM token', {
      userId: req.user?._id,
      deviceId: req.params.deviceId,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to remove token'
    }, 500);
  }
};

/**
 * Get user's FCM tokens
 * GET /fcm/tokens
 */
exports.getTokens = async (req, res) => {
  try {
    const userId = req.user._id;

    const user = await User.findById(userId).select('fcmTokens');
    if (!user) {
      return sendResponse(res, {
        success: false,
        error: ['User not found'],
        message: 'User not found'
      }, 404);
    }

    // Return tokens without the actual token values for security
    const tokens = user.fcmTokens.map(token => ({
      deviceId: token.deviceId,
      platform: token.platform,
      appVersion: token.appVersion,
      isActive: token.isActive,
      lastUsed: token.lastUsed,
      createdAt: token.createdAt,
      tokenPreview: token.token.substring(0, 20) + '...'
    }));

    return sendResponse(res, {
      success: true,
      data: {
        ...tokens[0], // Spread the first token data directly
        totalTokens: tokens.length,
        activeTokens: tokens.filter(t => t.isActive).length
      },
      message: 'Tokens retrieved successfully'
    });

  } catch (error) {
    Logger.error('Failed to get FCM tokens', {
      userId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to get tokens'
    }, 500);
  }
};

/**
 * Update notification preferences
 * PUT /fcm/preferences
 */
exports.updatePreferences = async (req, res) => {
  try {
    const userId = req.user._id;
    const { preferences } = req.body;

    if (!preferences || typeof preferences !== 'object') {
      return sendResponse(res, {
        success: false,
        error: ['Preferences object is required'],
        message: 'Invalid request data'
      }, 400);
    }

    const user = await User.findById(userId);
    if (!user) {
      return sendResponse(res, {
        success: false,
        error: ['User not found'],
        message: 'User not found'
      }, 404);
    }

    // Update notification preferences
    user.notificationPreferences = {
      ...user.notificationPreferences,
      ...preferences
    };

    await user.save();

    Logger.info('Notification preferences updated', {
      userId,
      preferences
    });

    return sendResponse(res, {
      success: true,
      data: {
        preferences: user.notificationPreferences,
        message: 'Preferences updated successfully'
      },
      message: 'Preferences updated'
    });

  } catch (error) {
    Logger.error('Failed to update notification preferences', {
      userId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to update preferences'
    }, 500);
  }
};

/**
 * Get notification preferences
 * GET /fcm/preferences
 */
exports.getPreferences = async (req, res) => {
  try {
    const userId = req.user._id;

    const user = await User.findById(userId).select('notificationPreferences');
    if (!user) {
      return sendResponse(res, {
        success: false,
        error: ['User not found'],
        message: 'User not found'
      }, 404);
    }

    return sendResponse(res, {
      success: true,
      data: {
        preferences: user.notificationPreferences || {}
      },
      message: 'Preferences retrieved successfully'
    });

  } catch (error) {
    Logger.error('Failed to get notification preferences', {
      userId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to get preferences'
    }, 500);
  }
};

/**
 * Subscribe to topic
 * POST /fcm/topic/subscribe
 */
exports.subscribeToTopic = async (req, res) => {
  try {
    const userId = req.user._id;
    const { topic } = req.body;

    if (!topic) {
      return sendResponse(res, {
        success: false,
        error: ['Topic is required'],
        message: 'Invalid request data'
      }, 400);
    }

    // Validate topic name (alphanumeric and underscores only)
    if (!/^[a-zA-Z0-9_]+$/.test(topic)) {
      return sendResponse(res, {
        success: false,
        error: ['Invalid topic name. Use only alphanumeric characters and underscores'],
        message: 'Invalid topic name'
      }, 400);
    }

    const result = await fcmService.subscribeToTopic(userId, topic);

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to subscribe to topic'
      }, 500);
    }

    Logger.info('User subscribed to topic', {
      userId,
      topic,
      successCount: result.successCount
    });

    return sendResponse(res, {
      success: true,
      data: {
        topic,
        successCount: result.successCount,
        failureCount: result.failureCount,
        message: 'Successfully subscribed to topic'
      },
      message: 'Subscribed to topic'
    });

  } catch (error) {
    Logger.error('Failed to subscribe to topic', {
      userId: req.user?._id,
      topic: req.body?.topic,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to subscribe to topic'
    }, 500);
  }
};

/**
 * Unsubscribe from topic
 * POST /fcm/topic/unsubscribe
 */
exports.unsubscribeFromTopic = async (req, res) => {
  try {
    const userId = req.user._id;
    const { topic } = req.body;

    if (!topic) {
      return sendResponse(res, {
        success: false,
        error: ['Topic is required'],
        message: 'Invalid request data'
      }, 400);
    }

    const result = await fcmService.unsubscribeFromTopic(userId, topic);

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to unsubscribe from topic'
      }, 500);
    }

    Logger.info('User unsubscribed from topic', {
      userId,
      topic,
      successCount: result.successCount
    });

    return sendResponse(res, {
      success: true,
      data: {
        topic,
        successCount: result.successCount,
        failureCount: result.failureCount,
        message: 'Successfully unsubscribed from topic'
      },
      message: 'Unsubscribed from topic'
    });

  } catch (error) {
    Logger.error('Failed to unsubscribe from topic', {
      userId: req.user?._id,
      topic: req.body?.topic,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to unsubscribe from topic'
    }, 500);
  }
};

/**
 * Send test notification
 * POST /fcm/test
 */
exports.sendTestNotification = async (req, res) => {
  try {
    const userId = req.user._id;
    const { type = 'custom', title, body, data = {} } = req.body;

    if (!title || !body) {
      return sendResponse(res, {
        success: false,
        error: ['Title and body are required'],
        message: 'Invalid request data'
      }, 400);
    }

    const notification = { title, body };
    const result = await fcmService.sendToUser(userId, notification, data, {
      notificationType: type
    });

    if (!result.success) {
      return sendResponse(res, {
        success: false,
        error: [result.error],
        message: 'Failed to send test notification'
      }, 500);
    }

    Logger.info('Test notification sent', {
      userId,
      type,
      sentCount: result.sentCount
    });

    return sendResponse(res, {
      success: true,
      data: {
        sentCount: result.sentCount,
        totalTokens: result.totalTokens,
        message: 'Test notification sent successfully'
      },
      message: 'Test notification sent'
    });

  } catch (error) {
    Logger.error('Failed to send test notification', {
      userId: req.user?._id,
      error: error.message
    });

    return sendResponse(res, {
      success: false,
      error: [error.message],
      message: 'Failed to send test notification'
    }, 500);
  }
};
