const mongoose = require('mongoose');
const User = require('./models/user');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function createTestUser() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Test user data
    const testUserData = {
      phone: '+919876543210',
      email: '<EMAIL>',
      nickname: 'TestUser',
      position: 'versatile',
      kamraHai: true,
      bio: 'Test user for payment integration',
      location: {
        type: 'Point',
        coordinates: [77.2090, 28.6139] // Delhi coordinates
      },
      isOnboarded: true
    };

    // Check if user already exists
    let existingUser = await User.findOne({ 
      $or: [
        { phone: testUserData.phone },
        { email: testUserData.email }
      ]
    });

    let user;
    if (existingUser) {
      console.log('📱 Test user already exists:', {
        id: existingUser._id,
        phone: existingUser.phone,
        email: existingUser.email,
        razorpayCustomerId: existingUser.razorpayCustomerId || 'Not set'
      });
      user = existingUser;
    } else {
      console.log('👤 Creating new test user...');
      user = await User.create(testUserData);
      console.log('✅ Test user created:', {
        id: user._id,
        phone: user.phone,
        email: user.email
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id.toString() },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('\n🔐 JWT Token Generated:');
    console.log(token);

    console.log('\n🧪 Test cURL Commands:');
    console.log('\n1. Get Subscription Plans:');
    console.log(`curl -X GET "http://localhost:3001/subscription/plans" \\`);
    console.log(`  -H "Content-Type: application/json"`);

    console.log('\n2. Create Monthly Recurring Subscription:');
    console.log(`curl -X POST "http://localhost:3001/subscription/create-order" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -d '{`);
    console.log(`    "planId": "plan_QkkDaTp9Hje6uC",`);
    console.log(`    "recurring": true`);
    console.log(`  }'`);

    console.log('\n3. Create Yearly Recurring Subscription:');
    console.log(`curl -X POST "http://localhost:3001/subscription/create-order" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -d '{`);
    console.log(`    "planId": "plan_QkkDw9QRHFT0nG",`);
    console.log(`    "recurring": true`);
    console.log(`  }'`);

    console.log('\n4. Create One-Time Order (Monthly):');
    console.log(`curl -X POST "http://localhost:3001/subscription/create-order" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -d '{`);
    console.log(`    "planId": "plan_QkkDaTp9Hje6uC",`);
    console.log(`    "recurring": false`);
    console.log(`  }'`);

    console.log('\n5. Get Subscription Status:');
    console.log(`curl -X GET "http://localhost:3001/subscription/status" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}"`);

    console.log('\n📝 User Details:');
    console.log(`User ID: ${user._id}`);
    console.log(`Phone: ${user.phone}`);
    console.log(`Email: ${user.email}`);
    console.log(`Razorpay Customer ID: ${user.razorpayCustomerId || 'Not set (will be created on first subscription)'}`);

    console.log('\n🎯 Ready for testing! Server is running on http://localhost:3001');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  createTestUser();
}

module.exports = createTestUser;
