#!/bin/bash

# Test script for updated subscription API with planId

API_BASE="http://localhost:3001/api"
TOKEN="your_jwt_token_here"  # Replace with actual token

echo "=== Testing Updated Subscription API with planId ==="
echo ""

echo "1. Testing GET /subscription/plans"
curl -X GET "$API_BASE/subscription/plans" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo ""

echo "2. Testing POST /subscription/create-payment with Monthly Plan"
curl -X POST "$API_BASE/subscription/create-payment" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"planId": "plan_QwQVKjofv1kEIX"}' | jq '.'

echo ""
echo ""

echo "3. Testing POST /subscription/create-payment with Yearly Plan"
curl -X POST "$API_BASE/subscription/create-payment" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"planId": "plan_QwQVupm00bZ4d9"}' | jq '.'

echo ""
echo ""

echo "4. Testing with Invalid planId (should fail)"
curl -X POST "$API_BASE/subscription/create-payment" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"planId": "invalid_plan_id"}' | jq '.'

echo ""
echo ""

echo "5. Testing with missing planId (should fail)"
curl -X POST "$API_BASE/subscription/create-payment" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}' | jq '.'
