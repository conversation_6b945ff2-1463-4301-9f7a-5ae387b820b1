/**
 * 🔒 GRANDFATHERING DEMONSTRATION SCRIPT
 * 
 * This script demonstrates the grandfathering logic in action.
 * Run this to see how the system preserves purchased benefits.
 */

const mongoose = require('mongoose');
const User = require('./models/user');
const Subscription = require('./models/subscription');
const PlanConfig = require('./models/planConfig');
const { getAppConfig } = require('./controllers/configController');

async function demonstrateGrandfathering() {
  try {
    console.log('🔒 GRANDFATHERING LOGIC DEMONSTRATION\n');

    // Step 1: Initial setup - unlimited monthly plan
    console.log('📋 Step 1: Setting up unlimited monthly plan...');
    await PlanConfig.findOneAndUpdate(
      { plan: 'monthly' },
      {
        plan: 'monthly',
        swipeLimitPerDay: -1,
        superMessageTokensPerMonth: -1,
        allowDirectMessage: true,
        validityInDays: 30
      },
      { upsert: true }
    );
    console.log('✅ Monthly plan configured: UNLIMITED swipes, UNLIMITED tokens\n');

    // Step 2: User purchases unlimited plan
    console.log('📋 Step 2: User purchases monthly plan...');
    const user = new User({
      name: 'Demo User',
      email: '<EMAIL>',
      phoneNumber: '+1234567890',
      isVerified: true,
      isPremium: true,
      plan: 'monthly',
      superMessageTokens: -1
    });
    await user.save();

    const subscription = new Subscription({
      userId: user._id,
      plan: 'monthly',
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      purchasedPlanConfig: {
        swipeLimitPerDay: -1,                // UNLIMITED when purchased
        superMessageTokensPerMonth: -1,      // UNLIMITED when purchased
        allowDirectMessage: true,
        validityInDays: 30
      },
      purchaseDate: new Date()
    });
    await subscription.save();

    user.subscriptionEndsOn = subscription.endDate;
    await user.save();

    console.log('✅ User purchased unlimited monthly plan');
    
    // Check user config
    const mockReq1 = { user };
    const mockRes1 = { json: () => {}, status: () => mockRes1 };
    let response1;
    mockRes1.json = (data) => { response1 = data; };
    
    await getAppConfig(mockReq1, mockRes1);
    console.log(`   🎯 User gets: ${response1.data.superMessageTokens === -1 ? 'UNLIMITED' : response1.data.superMessageTokens} tokens, ${response1.data.dailySwipeLimit === -1 ? 'UNLIMITED' : response1.data.dailySwipeLimit} swipes\n`);

    // Step 3: Admin changes plan to limited
    console.log('📋 Step 3: Admin changes monthly plan to limited...');
    await PlanConfig.findOneAndUpdate(
      { plan: 'monthly' },
      {
        swipeLimitPerDay: 50,               // NOW LIMITED
        superMessageTokensPerMonth: 30,     // NOW LIMITED
        allowDirectMessage: true,
        validityInDays: 30
      }
    );
    console.log('✅ Monthly plan changed: 50 swipes/day, 30 tokens/month');

    // Check existing user config (should be grandfathered)
    const mockReq2 = { user };
    const mockRes2 = { json: () => {}, status: () => mockRes2 };
    let response2;
    mockRes2.json = (data) => { response2 = data; };
    
    await getAppConfig(mockReq2, mockRes2);
    console.log(`   🔒 Existing user STILL gets: ${response2.data.superMessageTokens === -1 ? 'UNLIMITED' : response2.data.superMessageTokens} tokens, ${response2.data.dailySwipeLimit === -1 ? 'UNLIMITED' : response2.data.dailySwipeLimit} swipes (GRANDFATHERED!)\n`);

    // Step 4: New user purchases current limited plan
    console.log('📋 Step 4: New user purchases current limited plan...');
    const newUser = new User({
      name: 'New Demo User',
      email: '<EMAIL>',
      phoneNumber: '+1234567891',
      isVerified: true,
      isPremium: true,
      plan: 'monthly',
      superMessageTokens: 30
    });
    await newUser.save();

    const newSubscription = new Subscription({
      userId: newUser._id,
      plan: 'monthly',
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      purchasedPlanConfig: {
        swipeLimitPerDay: 50,               // LIMITED when purchased
        superMessageTokensPerMonth: 30,     // LIMITED when purchased
        allowDirectMessage: true,
        validityInDays: 30
      },
      purchaseDate: new Date()
    });
    await newSubscription.save();

    newUser.subscriptionEndsOn = newSubscription.endDate;
    await newUser.save();

    // Check new user config
    const mockReq3 = { user: newUser };
    const mockRes3 = { json: () => {}, status: () => mockRes3 };
    let response3;
    mockRes3.json = (data) => { response3 = data; };
    
    await getAppConfig(mockReq3, mockRes3);
    console.log(`✅ New user gets: ${response3.data.superMessageTokens} tokens, ${response3.data.dailySwipeLimit} swipes (CURRENT LIMITED PLAN)\n`);

    // Step 5: Original user renews subscription
    console.log('📋 Step 5: Original user renews subscription...');
    subscription.purchasedPlanConfig = {
      swipeLimitPerDay: 50,               // NOW LIMITED (renewal gets current config)
      superMessageTokensPerMonth: 30,     // NOW LIMITED (renewal gets current config)
      allowDirectMessage: true,
      validityInDays: 30
    };
    subscription.purchaseDate = new Date(); // New purchase date
    subscription.endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    await subscription.save();

    user.superMessageTokens = 30; // Reset to new limit
    await user.save();

    // Check renewed user config
    const mockReq4 = { user };
    const mockRes4 = { json: () => {}, status: () => mockRes4 };
    let response4;
    mockRes4.json = (data) => { response4 = data; };
    
    await getAppConfig(mockReq4, mockRes4);
    console.log(`✅ Renewed user gets: ${response4.data.superMessageTokens} tokens, ${response4.data.dailySwipeLimit} swipes (CURRENT LIMITED PLAN)\n`);

    console.log('🎉 GRANDFATHERING DEMONSTRATION COMPLETE!\n');
    console.log('📊 SUMMARY:');
    console.log('   • Existing users keep purchased benefits until expiry');
    console.log('   • New users get current plan configuration');
    console.log('   • Renewals get current plan configuration');
    console.log('   • Perfect grandfathering system! 🔒✨\n');

    // Cleanup
    await User.deleteMany({ email: { $in: ['<EMAIL>', '<EMAIL>'] } });
    await Subscription.deleteMany({ userId: { $in: [user._id, newUser._id] } });

  } catch (error) {
    console.error('❌ Error during demonstration:', error.message);
  }
}

// Export for manual testing
module.exports = { demonstrateGrandfathering };

// Run if called directly
if (require.main === module) {
  demonstrateGrandfathering().then(() => {
    console.log('Demo completed!');
    process.exit(0);
  }).catch(error => {
    console.error('Demo failed:', error);
    process.exit(1);
  });
}
