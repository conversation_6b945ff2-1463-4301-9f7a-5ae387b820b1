/**
 * FCM Error Handler Middleware
 * 
 * This middleware provides comprehensive error handling for FCM operations
 * including retry mechanisms, error categorization, and monitoring.
 * 
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

const Logger = require('../utils/logger');

class FCMErrorHandler {
  constructor() {
    this.retryableErrors = [
      'messaging/internal-error',
      'messaging/server-unavailable',
      'messaging/timeout',
      'messaging/quota-exceeded',
      'messaging/throttled'
    ];

    this.permanentErrors = [
      'messaging/invalid-registration-token',
      'messaging/registration-token-not-registered',
      'messaging/invalid-package-name',
      'messaging/invalid-argument',
      'messaging/authentication-error',
      'messaging/sender-id-mismatch'
    ];

    this.maxRetries = 3;
    this.baseDelay = 1000; // 1 second
  }

  /**
   * Handle FCM error with appropriate retry logic
   * @param {Error} error - FCM error
   * @param {Object} context - Error context
   * @param {number} attempt - Current attempt number
   * @returns {Object} Error handling result
   */
  handleError(error, context = {}, attempt = 1) {
    const errorCode = error.code || 'unknown';
    const errorMessage = error.message || 'Unknown error';

    const errorInfo = {
      code: errorCode,
      message: errorMessage,
      attempt,
      context,
      timestamp: new Date().toISOString(),
      isRetryable: this.isRetryableError(errorCode),
      isPermanent: this.isPermanentError(errorCode),
      shouldRetry: this.shouldRetry(errorCode, attempt)
    };

    // Log error based on severity
    this.logError(errorInfo);

    // Determine next action
    if (errorInfo.shouldRetry) {
      const delay = this.calculateRetryDelay(attempt);
      return {
        action: 'retry',
        delay,
        errorInfo
      };
    } else if (errorInfo.isPermanent) {
      return {
        action: 'permanent_failure',
        errorInfo
      };
    } else {
      return {
        action: 'temporary_failure',
        errorInfo
      };
    }
  }

  /**
   * Check if error is retryable
   * @param {string} errorCode - FCM error code
   * @returns {boolean}
   */
  isRetryableError(errorCode) {
    return this.retryableErrors.includes(errorCode);
  }

  /**
   * Check if error is permanent
   * @param {string} errorCode - FCM error code
   * @returns {boolean}
   */
  isPermanentError(errorCode) {
    return this.permanentErrors.includes(errorCode);
  }

  /**
   * Determine if should retry based on error code and attempt count
   * @param {string} errorCode - FCM error code
   * @param {number} attempt - Current attempt number
   * @returns {boolean}
   */
  shouldRetry(errorCode, attempt) {
    return this.isRetryableError(errorCode) && attempt < this.maxRetries;
  }

  /**
   * Calculate retry delay with exponential backoff
   * @param {number} attempt - Current attempt number
   * @returns {number} Delay in milliseconds
   */
  calculateRetryDelay(attempt) {
    // Exponential backoff with jitter
    const exponentialDelay = this.baseDelay * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 0.1 * exponentialDelay;
    return Math.floor(exponentialDelay + jitter);
  }

  /**
   * Log error with appropriate level
   * @param {Object} errorInfo - Error information
   */
  logError(errorInfo) {
    const logData = {
      fcmError: true,
      errorCode: errorInfo.code,
      message: errorInfo.message,
      attempt: errorInfo.attempt,
      isRetryable: errorInfo.isRetryable,
      isPermanent: errorInfo.isPermanent,
      shouldRetry: errorInfo.shouldRetry,
      context: errorInfo.context,
      timestamp: errorInfo.timestamp
    };

    if (errorInfo.isPermanent) {
      Logger.error('FCM permanent error', logData);
    } else if (errorInfo.isRetryable) {
      Logger.warn('FCM retryable error', logData);
    } else {
      Logger.error('FCM unknown error', logData);
    }
  }

  /**
   * Create retry function with error handling
   * @param {Function} operation - Operation to retry
   * @param {Object} context - Operation context
   * @returns {Function} Retry function
   */
  createRetryFunction(operation, context = {}) {
    return async (attempt = 1) => {
      try {
        return await operation();
      } catch (error) {
        const result = this.handleError(error, context, attempt);

        if (result.action === 'retry') {
          Logger.info('Retrying FCM operation', {
            attempt: attempt + 1,
            delay: result.delay,
            context
          });

          await this.delay(result.delay);
          return this.createRetryFunction(operation, context)(attempt + 1);
        } else {
          throw error;
        }
      }
    };
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Monitor FCM operation performance
   * @param {string} operation - Operation name
   * @param {Function} fn - Function to monitor
   * @param {Object} context - Operation context
   * @returns {Function} Monitored function
   */
  monitorOperation(operation, fn, context = {}) {
    return async (...args) => {
      const startTime = Date.now();
      const operationId = `${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      Logger.info('FCM operation started', {
        operationId,
        operation,
        context,
        startTime
      });

      try {
        const result = await fn(...args);
        const duration = Date.now() - startTime;

        Logger.info('FCM operation completed', {
          operationId,
          operation,
          duration,
          success: true,
          context
        });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;

        Logger.error('FCM operation failed', {
          operationId,
          operation,
          duration,
          success: false,
          error: error.message,
          errorCode: error.code,
          context
        });

        throw error;
      }
    };
  }

  /**
   * Validate FCM configuration
   * @returns {Object} Validation result
   */
  validateConfiguration() {
    const admin = require('../firebaseAdmin');
    
    if (!admin) {
      return {
        valid: false,
        errors: ['Firebase Admin SDK not initialized']
      };
    }

    try {
      // Test if messaging is available
      const messaging = admin.messaging();
      
      return {
        valid: true,
        errors: []
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`Firebase messaging not available: ${error.message}`]
      };
    }
  }

  /**
   * Get error statistics
   * @param {Date} startDate - Start date for statistics
   * @param {Date} endDate - End date for statistics
   * @returns {Object} Error statistics
   */
  getErrorStatistics(startDate, endDate) {
    // This would typically query a logging/monitoring system
    // For now, return a placeholder structure
    return {
      totalErrors: 0,
      retryableErrors: 0,
      permanentErrors: 0,
      unknownErrors: 0,
      topErrors: [],
      errorsByHour: [],
      period: {
        start: startDate,
        end: endDate
      }
    };
  }
}

// Export singleton instance
module.exports = new FCMErrorHandler();
