/**
 * Package ID Middleware for Milo-SVC
 * 
 * Handles x-package-id header for multi-tenant support
 * Based on seekho-backend pattern but adapted for milo-svc
 */

const { sendResponse } = require('../utils/response');

// Supported package IDs for milo-svc
const SUPPORTED_PACKAGES = [
  'com.milo.dating',
  'com.app.milo' // Alternative package ID
];

/**
 * Validate package ID format
 * @param {string} packageId - Package ID to validate
 * @returns {boolean} True if valid format
 */
const isValidPackageIdFormat = (packageId) => {
  if (!packageId || typeof packageId !== 'string') {
    return false;
  }
  
  // Basic format validation: should be like com.company.app
  const packageIdRegex = /^[a-z]+\.[a-z]+\.[a-z]+$/;
  return packageIdRegex.test(packageId);
};

/**
 * Get app name from package ID
 * @param {string} packageId - Package ID
 * @returns {string} App name
 */
const getAppNameFromPackageId = (packageId) => {
  switch (packageId) {
    case 'com.milo.dating':
      return 'Milo Dating';
    case 'com.app.milo':
      return 'Milo App';
    default:
      return 'Unknown App';
  }
};

/**
 * Extract package ID from request headers
 * Required middleware - fails if package ID is missing or invalid
 */
const extractPackageId = (req, res, next) => {
  try {
    // Get package ID from x-package-id header (case insensitive)
    let packageId = req.headers['x-package-id'] || req.headers['X-Package-ID'];
    
    // If no package ID provided, default to milo dating app
    if (!packageId) {
      packageId = 'com.milo.dating';
      console.log('No package ID provided, defaulting to Milo Dating app');
    }

    // Validate package ID format
    if (!isValidPackageIdFormat(packageId)) {
      return sendResponse(res, {
        success: false,
        error: ['Invalid package ID format'],
        message: 'Package ID must be in format: com.company.app'
      }, 400);
    }

    // Validate against supported packages
    if (!SUPPORTED_PACKAGES.includes(packageId)) {
      return sendResponse(res, {
        success: false,
        error: ['Unsupported package ID'],
        message: `Package ID '${packageId}' is not supported`,
        data: {
          supportedPackages: SUPPORTED_PACKAGES
        }
      }, 400);
    }

    // Add package ID to request
    req.packageId = packageId;
    
    console.log(`📦 Package ID: ${packageId} (${getAppNameFromPackageId(packageId)})`);
    next();
  } catch (error) {
    console.error('Package ID extraction error:', error);
    return sendResponse(res, {
      success: false,
      error: ['Package ID processing failed'],
      message: 'Internal server error'
    }, 500);
  }
};

/**
 * Optional package ID extraction - doesn't fail if missing
 */
const optionalPackageId = (req, res, next) => {
  try {
    // Extract package ID if present
    let packageId = req.headers['x-package-id'] || req.headers['X-Package-ID'];
    
    if (packageId) {
      // Validate format if provided
      if (!isValidPackageIdFormat(packageId)) {
        return sendResponse(res, {
          success: false,
          error: ['Invalid package ID format'],
          message: 'Package ID must be in format: com.company.app'
        }, 400);
      }

      // Validate against supported packages
      if (!SUPPORTED_PACKAGES.includes(packageId)) {
        return sendResponse(res, {
          success: false,
          error: ['Unsupported package ID'],
          message: `Package ID '${packageId}' is not supported`
        }, 400);
      }

      req.packageId = packageId;
    } else {
      // Default to Milo Dating for backward compatibility
      req.packageId = 'com.milo.dating';
    }

    console.log(`📦 Package ID: ${req.packageId} (${getAppNameFromPackageId(req.packageId)})`);
    next();
  } catch (error) {
    console.error('Optional package ID extraction error:', error);
    // Don't fail for optional middleware
    req.packageId = 'com.milo.dating'; // Default fallback
    next();
  }
};

/**
 * Middleware to add package ID to response headers (for debugging)
 */
const addPackageIdToResponse = (req, res, next) => {
  if (req.packageId) {
    res.set('X-Response-Package-ID', req.packageId);
    res.set('X-App-Name', getAppNameFromPackageId(req.packageId));
  }
  next();
};

/**
 * Check if package ID is for Milo Dating app
 */
const isMiloApp = (packageId) => {
  return packageId === 'com.milo.dating' || packageId === 'com.app.milo';
};

// Combined middleware for full package ID processing
const processPackageId = [extractPackageId, addPackageIdToResponse];

module.exports = {
  extractPackageId,
  optionalPackageId,
  processPackageId,
  addPackageIdToResponse,
  isValidPackageIdFormat,
  getAppNameFromPackageId,
  isMiloApp,
  SUPPORTED_PACKAGES
};
