# PowerShell script to test updated payment status API

$API_BASE = "http://localhost:3001/api"
$TOKEN = "your_jwt_token_here"  # Replace with actual token

$headers = @{
    "Authorization" = "Bearer $TOKEN"
    "Content-Type" = "application/json"
}

Write-Host "=== Testing Updated Payment Status API ===" -ForegroundColor Green
Write-Host ""

# Test 1: Check current payment status (existing behavior)
Write-Host "1. Checking current payment status..." -ForegroundColor Yellow
$checkStatusBody = @{
    paymentId = "688355b4e0f0cbe87f4fdf77"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/payment-status" -Method POST -Headers $headers -Body $checkStatusBody
    Write-Host "Current Status Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""

# Test 2: Mark payment as successful
Write-Host "2. Marking payment as successful..." -ForegroundColor Yellow
$successBody = @{
    paymentId = "688355b4e0f0cbe87f4fdf77"
    status = "captured"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/payment-status" -Method POST -Headers $headers -Body $successBody
    Write-Host "Success Response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""

# Test 3: Mark payment as failed
Write-Host "3. Marking payment as failed..." -ForegroundColor Yellow
$failedBody = @{
    paymentId = "688355b4e0f0cbe87f4fdf77"
    status = "failed"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/payment-status" -Method POST -Headers $headers -Body $failedBody
    Write-Host "Failed Response:" -ForegroundColor Red
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""

# Test 4: Invalid status
Write-Host "4. Testing invalid status..." -ForegroundColor Yellow
$invalidBody = @{
    paymentId = "688355b4e0f0cbe87f4fdf77"
    status = "invalid_status"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/payment-status" -Method POST -Headers $headers -Body $invalidBody
    Write-Host "Invalid Status Response:" -ForegroundColor Magenta
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Expected Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Instructions ===" -ForegroundColor Green
Write-Host "1. Replace 'your_jwt_token_here' with actual JWT token"
Write-Host "2. Replace paymentId with actual payment ID from your database"
Write-Host "3. Ensure your server is running on localhost:3001"
Write-Host "4. Frontend should call with appropriate status based on payment result"
