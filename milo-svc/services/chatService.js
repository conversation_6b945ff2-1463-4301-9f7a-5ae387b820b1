const Match = require('../models/match');
const Message = require('../models/message');
const User = require('../models/user');
const BlockedUser = require('../models/blockedUser');
const mongoose = require('mongoose');

const MAX_PREVIEW_LENGTH = 40;

const getChatListForUser = async (userId) => {
  userId = new mongoose.Types.ObjectId(userId);

  // Step 1: Fetch all matches for the user
  const matches = await Match.find({ users: userId });
  if (!matches.length) return [];

  // Step 2: Map matchId => otherUserId
  const matchIdToOtherUserId = {};
  for (const match of matches) {
    const otherUserId = match.users.find(id => id.toString() !== userId.toString());
    if (!otherUserId) continue; // skip malformed matches
    matchIdToOtherUserId[match._id.toString()] = otherUserId.toString();
  }

  const allOtherUserIds = Object.values(matchIdToOtherUserId);
  const allMatchIds = Object.keys(matchIdToOtherUserId);

  // Step 3: Filter out blocked users
  const blocked = await BlockedUser.find({
    $or: [{ blocker: userId }, { blocked: userId }]
  });

  const blockedUserIds = new Set(
    blocked.flatMap(b => [b.blocker.toString(), b.blocked.toString()])
  );

  const filteredMatchIds = allMatchIds.filter(matchId => {
    const otherId = matchIdToOtherUserId[matchId];
    return !blockedUserIds.has(otherId);
  });

  const filteredOtherUserIds = filteredMatchIds.map(matchId => matchIdToOtherUserId[matchId]);

  // Step 4: Fetch user info in batch
  const users = await User.find({ _id: { $in: filteredOtherUserIds } })
    .select('_id nickname')
    .lean();

  const userMap = Object.fromEntries(users.map(u => [u._id.toString(), u]));

  // Step 5: Aggregate latest messages per match
  const latestMessages = await Message.aggregate([
    {
      $match: {
        matchId: {
          $in: filteredMatchIds.map(id => new mongoose.Types.ObjectId(id))
        }
      }
    },
    { $sort: { timestamp: -1 } },
    {
      $group: {
        _id: '$matchId',
        text: { $first: '$text' },
        fromUser: { $first: '$fromUser' },
        timestamp: { $first: '$timestamp' },
        read: { $first: '$read' },
        status: { $first: '$status' }
      }
    },
    { $project: { _id: 1, text: 1, fromUser: 1, timestamp: 1, read: 1, status: 1 } }
  ]);

  const messageMap = Object.fromEntries(
    latestMessages.map(m => {
      const previewText =
        typeof m.text === 'string' && m.text.length > MAX_PREVIEW_LENGTH
          ? m.text.slice(0, MAX_PREVIEW_LENGTH - 3) + '...'
          : m.text;

      return [
        m._id.toString(),
        {
          text: previewText,
          sender: m.fromUser,
          createdAt: m.timestamp ? (m.timestamp instanceof Date ? m.timestamp.getTime() : new Date(m.timestamp).getTime()) : 0,
          read: m.read || false,
          status: m.status || 'sent'
        }
      ];
    })
  );

  // Step 6: Aggregate unread counts per match
  const unreadCounts = await Message.aggregate([
    {
      $match: {
        matchId: {
          $in: filteredMatchIds.map(id => new mongoose.Types.ObjectId(id))
        },
        fromUser: { $ne: userId },
        read: false
      }
    },
    {
      $group: {
        _id: '$matchId',
        count: { $sum: 1 }
      }
    }
  ]);

  const unreadMap = Object.fromEntries(unreadCounts.map(u => [u._id.toString(), u.count]));

  // Step 7: Assemble final chat list
  const chatList = [];

  for (const matchId of filteredMatchIds) {
    const otherUserId = matchIdToOtherUserId[matchId];
    const user = userMap[otherUserId];
    if (!user) continue; // skip if user is deleted or not found


    const lastMessage = messageMap[matchId];
    // Fix: compare userId with lastMessage.sender (which is fromUser in aggregation)
    const isSender = lastMessage?.sender?.toString() === userId.toString() || lastMessage?.fromUser?.toString() === userId.toString();

    const chatItem = {
      matchId,
      userId: user._id,
      nickname: user.nickname,
      isNew: !lastMessage,
      lastMessageTime: lastMessage?.createdAt || 0,
      lastMessage: lastMessage?.text || '',
      isSender
    };

    if (lastMessage && isSender) {
      chatItem.isRead = lastMessage.read === true;
      chatItem.status = lastMessage.status || 'sent';
    }

    if (lastMessage && !isSender) {
      chatItem.unreadCount = unreadMap[matchId] || 0;
    }

    // if (lastMessage) {                for object in future
    //   chatItem.lastMessage = {
    //     text: lastMessage.text,
    //     sender: lastMessage.sender,
    //     createdAt: lastMessage.createdAt,
    //     isRead: lastMessage.sender?.toString() !== userId.toString()
    //       ? lastMessage.isRead || false
    //       : true
    //   };
    // }

    chatList.push(chatItem);
  }

  // Step 8: Sort by latest activity
  chatList.sort((a, b) => b.lastMessageTime - a.lastMessageTime);

  return chatList;
};

module.exports = { getChatListForUser };


