/**
 * Notification Templates Service
 * 
 * This service provides standardized notification templates for different
 * types of push notifications with localization support.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

const Logger = require('../utils/logger');

class NotificationTemplates {
  constructor() {
    this.defaultLanguage = 'en';
    this.supportedLanguages = ['en', 'hi']; // English, Hindi
  }

  /**
   * Get notification template for new message
   * @param {Object} data - Message data
   * @param {string} language - Language code
   * @returns {Object}
   */
  getNewMessageTemplate(data, language = 'en') {
    const { senderName, messagePreview, matchId } = data;
    
    const templates = {
      en: {
        title: `New message from ${senderName}`,
        body: messagePreview || 'You have a new message',
        imageUrl: null
      },
      hi: {
        title: `${senderName} से नया संदेश`,
        body: messagePreview || 'आपके पास एक नया संदेश है',
        imageUrl: null
      }
    };

    return {
      notification: templates[language] || templates[this.defaultLanguage],
      data: {
        type: 'new_message',
        matchId: matchId?.toString(),
        senderId: data.senderId?.toString(),
        messageId: data.messageId?.toString(),
        action: 'open_chat'
      },
      options: {
        notificationType: 'new_message',
        android: {
          channelId: 'messages',
          priority: 'high',
          sound: 'message_sound',
          color: '#FF6B6B'
        },
        ios: {
          sound: 'message_sound.wav',
          category: 'MESSAGE_CATEGORY',
          threadId: `match_${matchId}`
        },
        web: {
          icon: '/icons/message-icon.png',
          tag: `message_${data.messageId}`,
          actions: [
            {
              action: 'reply',
              title: language === 'hi' ? 'जवाब दें' : 'Reply'
            },
            {
              action: 'view',
              title: language === 'hi' ? 'देखें' : 'View'
            }
          ]
        }
      }
    };
  }

  /**
   * Get notification template for new match
   * @param {Object} data - Match data
   * @param {string} language - Language code
   * @returns {Object}
   */
  getNewMatchTemplate(data, language = 'en') {
    const { matchedUserName, matchId } = data;
    
    const templates = {
      en: {
        title: 'It\'s a Match! 🎉',
        body: `You and ${matchedUserName} liked each other!`,
        imageUrl: data.matchedUserPhoto || null
      },
      hi: {
        title: 'यह एक मैच है! 🎉',
        body: `आप और ${matchedUserName} ने एक-दूसरे को पसंद किया!`,
        imageUrl: data.matchedUserPhoto || null
      }
    };

    return {
      notification: templates[language] || templates[this.defaultLanguage],
      data: {
        type: 'new_match',
        matchId: matchId?.toString(),
        matchedUserId: data.matchedUserId?.toString(),
        action: 'open_match'
      },
      options: {
        notificationType: 'new_match',
        android: {
          channelId: 'matches',
          priority: 'high',
          sound: 'match_sound',
          color: '#4ECDC4'
        },
        ios: {
          sound: 'match_sound.wav',
          category: 'MATCH_CATEGORY'
        },
        web: {
          icon: '/icons/match-icon.png',
          tag: `match_${matchId}`,
          actions: [
            {
              action: 'message',
              title: language === 'hi' ? 'संदेश भेजें' : 'Send Message'
            },
            {
              action: 'view_profile',
              title: language === 'hi' ? 'प्रोफ़ाइल देखें' : 'View Profile'
            }
          ]
        }
      }
    };
  }

  /**
   * Get notification template for super message
   * @param {Object} data - Super message data
   * @param {string} language - Language code
   * @returns {Object}
   */
  getSuperMessageTemplate(data, language = 'en') {
    const { senderName, messagePreview, matchId } = data;
    
    const templates = {
      en: {
        title: `⭐ Super Message from ${senderName}`,
        body: messagePreview || 'You received a super message!',
        imageUrl: null
      },
      hi: {
        title: `⭐ ${senderName} से सुपर संदेश`,
        body: messagePreview || 'आपको एक सुपर संदेश मिला!',
        imageUrl: null
      }
    };

    return {
      notification: templates[language] || templates[this.defaultLanguage],
      data: {
        type: 'super_message',
        matchId: matchId?.toString(),
        senderId: data.senderId?.toString(),
        messageId: data.messageId?.toString(),
        action: 'open_chat'
      },
      options: {
        notificationType: 'super_message',
        android: {
          channelId: 'super_messages',
          priority: 'high',
          sound: 'super_message_sound',
          color: '#FFD700'
        },
        ios: {
          sound: 'super_message_sound.wav',
          category: 'SUPER_MESSAGE_CATEGORY',
          threadId: `match_${matchId}`
        },
        web: {
          icon: '/icons/super-message-icon.png',
          tag: `super_message_${data.messageId}`,
          actions: [
            {
              action: 'reply',
              title: language === 'hi' ? 'जवाब दें' : 'Reply'
            }
          ]
        }
      }
    };
  }

  /**
   * Get notification template for subscription expiry warning
   * @param {Object} data - Subscription data
   * @param {string} language - Language code
   * @returns {Object}
   */
  getSubscriptionExpiryTemplate(data, language = 'en') {
    const { daysLeft } = data;
    
    const templates = {
      en: {
        title: 'Subscription Expiring Soon',
        body: `Your premium subscription expires in ${daysLeft} days. Renew now to continue enjoying premium features!`,
        imageUrl: null
      },
      hi: {
        title: 'सब्सक्रिप्शन जल्द समाप्त हो रही है',
        body: `आपकी प्रीमियम सब्सक्रिप्शन ${daysLeft} दिनों में समाप्त हो जाएगी। प्रीमियम सुविधाओं का आनंद लेना जारी रखने के लिए अभी नवीनीकरण करें!`,
        imageUrl: null
      }
    };

    return {
      notification: templates[language] || templates[this.defaultLanguage],
      data: {
        type: 'subscription_expiry',
        daysLeft: daysLeft?.toString(),
        action: 'open_subscription'
      },
      options: {
        notificationType: 'system_update',
        android: {
          channelId: 'system',
          priority: 'normal',
          sound: 'default',
          color: '#FF9500'
        },
        ios: {
          sound: 'default',
          category: 'SUBSCRIPTION_CATEGORY'
        },
        web: {
          icon: '/icons/subscription-icon.png',
          tag: 'subscription_expiry',
          actions: [
            {
              action: 'renew',
              title: language === 'hi' ? 'नवीनीकरण करें' : 'Renew Now'
            }
          ]
        }
      }
    };
  }

  /**
   * Get notification template for promotional messages
   * @param {Object} data - Promotion data
   * @param {string} language - Language code
   * @returns {Object}
   */
  getPromotionTemplate(data, language = 'en') {
    const { title, message, imageUrl, actionUrl } = data;
    
    const templates = {
      en: {
        title: title || 'Special Offer for You! 🎁',
        body: message || 'Don\'t miss out on this exclusive offer!',
        imageUrl: imageUrl || null
      },
      hi: {
        title: title || 'आपके लिए विशेष ऑफर! 🎁',
        body: message || 'इस विशेष ऑफर को न चूकें!',
        imageUrl: imageUrl || null
      }
    };

    return {
      notification: templates[language] || templates[this.defaultLanguage],
      data: {
        type: 'promotion',
        actionUrl: actionUrl || '',
        action: 'open_url'
      },
      options: {
        notificationType: 'promotion',
        android: {
          channelId: 'promotions',
          priority: 'normal',
          sound: 'default',
          color: '#9C27B0'
        },
        ios: {
          sound: 'default',
          category: 'PROMOTION_CATEGORY'
        },
        web: {
          icon: '/icons/promotion-icon.png',
          tag: 'promotion',
          actions: [
            {
              action: 'view_offer',
              title: language === 'hi' ? 'ऑफर देखें' : 'View Offer'
            }
          ]
        }
      }
    };
  }

  /**
   * Get notification template for system updates
   * @param {Object} data - System update data
   * @param {string} language - Language code
   * @returns {Object}
   */
  getSystemUpdateTemplate(data, language = 'en') {
    const { title, message, updateType } = data;
    
    const templates = {
      en: {
        title: title || 'App Update Available',
        body: message || 'A new version of the app is available with exciting features!',
        imageUrl: null
      },
      hi: {
        title: title || 'ऐप अपडेट उपलब्ध है',
        body: message || 'रोमांचक सुविधाओं के साथ ऐप का नया संस्करण उपलब्ध है!',
        imageUrl: null
      }
    };

    return {
      notification: templates[language] || templates[this.defaultLanguage],
      data: {
        type: 'system_update',
        updateType: updateType || 'general',
        action: 'open_app'
      },
      options: {
        notificationType: 'system_update',
        android: {
          channelId: 'system',
          priority: 'normal',
          sound: 'default',
          color: '#2196F3'
        },
        ios: {
          sound: 'default',
          category: 'SYSTEM_CATEGORY'
        },
        web: {
          icon: '/icons/system-icon.png',
          tag: 'system_update'
        }
      }
    };
  }

  /**
   * Get custom notification template
   * @param {Object} data - Custom notification data
   * @param {string} language - Language code
   * @returns {Object}
   */
  getCustomTemplate(data, language = 'en') {
    const { title, body, imageUrl, type, customData } = data;
    
    return {
      notification: {
        title: title || 'Notification',
        body: body || 'You have a new notification',
        imageUrl: imageUrl || null
      },
      data: {
        type: type || 'custom',
        ...customData,
        action: data.action || 'open_app'
      },
      options: {
        notificationType: type || 'custom',
        android: {
          channelId: data.channelId || 'default',
          priority: data.priority || 'normal',
          sound: data.sound || 'default',
          color: data.color || '#000000'
        },
        ios: {
          sound: data.sound || 'default',
          category: data.category || 'DEFAULT_CATEGORY'
        },
        web: {
          icon: data.icon || '/icons/default-icon.png',
          tag: data.tag || 'custom_notification'
        }
      }
    };
  }
}

// Export singleton instance
module.exports = new NotificationTemplates();
