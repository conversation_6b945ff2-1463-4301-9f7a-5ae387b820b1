/**
 * Firebase Cloud Messaging (FCM) Service
 * 
 * This service handles all FCM operations including:
 * - Sending push notifications using FCM V1 API
 * - Managing FCM tokens
 * - Topic management
 * - Batch messaging
 * - Error handling and retry logic
 * 
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

const admin = require('../firebaseAdmin');
const Logger = require('../utils/logger');
const User = require('../models/user');

class FCMService {
  constructor() {
    this.admin = admin;
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
    this.batchSize = 500; // FCM batch limit
  }

  /**
   * Check if FCM is available
   * @returns {boolean}
   */
  isAvailable() {
    return this.admin !== null;
  }

  /**
   * Send notification to a single user
   * @param {string} userId - User ID
   * @param {Object} notification - Notification payload
   * @param {Object} data - Data payload
   * @param {Object} options - Additional options
   * @returns {Promise<Object>}
   */
  async sendToUser(userId, notification, data = {}, options = {}) {
    if (!this.isAvailable()) {
      Logger.warn('FCM not available - skipping notification', { userId });
      return { success: false, error: 'FCM not configured' };
    }

    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const tokens = user.getActiveFCMTokens();
      if (tokens.length === 0) {
        Logger.info('No active FCM tokens for user', { userId });
        return { success: true, message: 'No active tokens', sentCount: 0 };
      }

      // Check notification preferences
      if (!this.shouldSendNotification(user, options.notificationType)) {
        Logger.info('Notification blocked by user preferences', { 
          userId, 
          notificationType: options.notificationType 
        });
        return { success: true, message: 'Blocked by preferences', sentCount: 0 };
      }

      // Check quiet hours
      if (this.isQuietHours(user)) {
        Logger.info('Notification blocked by quiet hours', { userId });
        return { success: true, message: 'Blocked by quiet hours', sentCount: 0 };
      }

      const message = this.buildMessage(notification, data, options);
      const results = await this.sendToTokens(tokens, message);

      // Handle invalid tokens
      await this.handleInvalidTokens(userId, tokens, results);

      const successCount = results.filter(r => r.success).length;
      
      Logger.info('Notification sent to user', {
        userId,
        tokensCount: tokens.length,
        successCount,
        notificationType: options.notificationType
      });

      return {
        success: true,
        sentCount: successCount,
        totalTokens: tokens.length,
        results
      };

    } catch (error) {
      Logger.error('Failed to send notification to user', {
        userId,
        error: error.message,
        stack: error.stack
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send notification to multiple users
   * @param {Array<string>} userIds - Array of user IDs
   * @param {Object} notification - Notification payload
   * @param {Object} data - Data payload
   * @param {Object} options - Additional options
   * @returns {Promise<Object>}
   */
  async sendToUsers(userIds, notification, data = {}, options = {}) {
    if (!this.isAvailable()) {
      Logger.warn('FCM not available - skipping batch notification');
      return { success: false, error: 'FCM not configured' };
    }

    try {
      const results = [];
      const batchPromises = [];

      // Process users in batches
      for (let i = 0; i < userIds.length; i += this.batchSize) {
        const batch = userIds.slice(i, i + this.batchSize);
        batchPromises.push(this.processBatch(batch, notification, data, options));
      }

      const batchResults = await Promise.allSettled(batchPromises);
      
      let totalSent = 0;
      let totalUsers = 0;

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          totalSent += result.value.sentCount;
          totalUsers += result.value.totalUsers;
          results.push(...result.value.results);
        } else {
          Logger.error('Batch processing failed', {
            batchIndex: index,
            error: result.reason
          });
        }
      });

      Logger.info('Batch notification completed', {
        totalUsers,
        totalSent,
        batchCount: batchPromises.length
      });

      return {
        success: true,
        totalUsers,
        totalSent,
        results
      };

    } catch (error) {
      Logger.error('Failed to send batch notifications', {
        userCount: userIds.length,
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send notification to a topic
   * @param {string} topic - Topic name
   * @param {Object} notification - Notification payload
   * @param {Object} data - Data payload
   * @param {Object} options - Additional options
   * @returns {Promise<Object>}
   */
  async sendToTopic(topic, notification, data = {}, options = {}) {
    if (!this.isAvailable()) {
      Logger.warn('FCM not available - skipping topic notification');
      return { success: false, error: 'FCM not configured' };
    }

    try {
      const message = {
        topic,
        ...this.buildMessage(notification, data, options)
      };

      const response = await this.admin.messaging().send(message);
      
      Logger.info('Topic notification sent', {
        topic,
        messageId: response,
        notificationType: options.notificationType
      });

      return {
        success: true,
        messageId: response,
        topic
      };

    } catch (error) {
      Logger.error('Failed to send topic notification', {
        topic,
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Subscribe user to topic
   * @param {string} userId - User ID
   * @param {string} topic - Topic name
   * @returns {Promise<Object>}
   */
  async subscribeToTopic(userId, topic) {
    if (!this.isAvailable()) {
      return { success: false, error: 'FCM not configured' };
    }

    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const tokens = user.getActiveFCMTokens();
      if (tokens.length === 0) {
        return { success: true, message: 'No active tokens' };
      }

      const response = await this.admin.messaging().subscribeToTopic(tokens, topic);
      
      Logger.info('User subscribed to topic', {
        userId,
        topic,
        tokensCount: tokens.length,
        successCount: response.successCount,
        failureCount: response.failureCount
      });

      return {
        success: true,
        successCount: response.successCount,
        failureCount: response.failureCount,
        errors: response.errors
      };

    } catch (error) {
      Logger.error('Failed to subscribe user to topic', {
        userId,
        topic,
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Unsubscribe user from topic
   * @param {string} userId - User ID
   * @param {string} topic - Topic name
   * @returns {Promise<Object>}
   */
  async unsubscribeFromTopic(userId, topic) {
    if (!this.isAvailable()) {
      return { success: false, error: 'FCM not configured' };
    }

    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const tokens = user.getActiveFCMTokens();
      if (tokens.length === 0) {
        return { success: true, message: 'No active tokens' };
      }

      const response = await this.admin.messaging().unsubscribeFromTopic(tokens, topic);
      
      Logger.info('User unsubscribed from topic', {
        userId,
        topic,
        tokensCount: tokens.length,
        successCount: response.successCount,
        failureCount: response.failureCount
      });

      return {
        success: true,
        successCount: response.successCount,
        failureCount: response.failureCount,
        errors: response.errors
      };

    } catch (error) {
      Logger.error('Failed to unsubscribe user from topic', {
        userId,
        topic,
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate FCM token
   * @param {string} token - FCM token
   * @returns {Promise<boolean>}
   */
  async validateToken(token) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const message = {
        token,
        data: { test: 'validation' }
      };

      await this.admin.messaging().send(message, true); // dry run
      return true;
    } catch (error) {
      Logger.warn('Invalid FCM token', { token: token.substring(0, 20) + '...', error: error.message });
      return false;
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Build FCM message object
   * @param {Object} notification - Notification payload
   * @param {Object} data - Data payload
   * @param {Object} options - Additional options
   * @returns {Object}
   */
  buildMessage(notification, data = {}, options = {}) {
    const message = {
      notification: {
        title: notification.title,
        body: notification.body,
        ...(notification.imageUrl && { imageUrl: notification.imageUrl })
      },
      data: {
        ...data,
        timestamp: new Date().toISOString(),
        ...(options.notificationType && { notificationType: options.notificationType })
      }
    };

    // Add Android specific configuration
    if (options.android) {
      message.android = {
        priority: options.android.priority || 'high',
        notification: {
          channelId: options.android.channelId || 'default',
          sound: options.android.sound || 'default',
          ...(options.android.color && { color: options.android.color }),
          ...(options.android.icon && { icon: options.android.icon })
        },
        ...(options.android.data && { data: options.android.data })
      };
    }

    // Add iOS specific configuration
    if (options.ios) {
      message.apns = {
        headers: {
          'apns-priority': options.ios.priority || '10'
        },
        payload: {
          aps: {
            sound: options.ios.sound || 'default',
            badge: options.ios.badge,
            ...(options.ios.category && { category: options.ios.category }),
            ...(options.ios.threadId && { 'thread-id': options.ios.threadId })
          }
        }
      };
    }

    // Add web specific configuration
    if (options.web) {
      message.webpush = {
        headers: {
          TTL: options.web.ttl || '86400'
        },
        notification: {
          icon: options.web.icon || '/icon-192x192.png',
          badge: options.web.badge || '/badge-72x72.png',
          ...(options.web.actions && { actions: options.web.actions }),
          ...(options.web.tag && { tag: options.web.tag })
        }
      };
    }

    return message;
  }

  /**
   * Send messages to multiple tokens
   * @param {Array<string>} tokens - FCM tokens
   * @param {Object} message - Message object
   * @returns {Promise<Array>}
   */
  async sendToTokens(tokens, message) {
    const results = [];

    for (const token of tokens) {
      try {
        const tokenMessage = { ...message, token };
        const response = await this.admin.messaging().send(tokenMessage);
        results.push({ token, success: true, messageId: response });
      } catch (error) {
        results.push({
          token,
          success: false,
          error: error.message,
          errorCode: error.code
        });
      }
    }

    return results;
  }

  /**
   * Process a batch of users
   * @param {Array<string>} userIds - User IDs
   * @param {Object} notification - Notification payload
   * @param {Object} data - Data payload
   * @param {Object} options - Additional options
   * @returns {Promise<Object>}
   */
  async processBatch(userIds, notification, data, options) {
    const results = [];
    let sentCount = 0;

    for (const userId of userIds) {
      const result = await this.sendToUser(userId, notification, data, options);
      results.push({ userId, ...result });
      if (result.success) {
        sentCount += result.sentCount || 0;
      }
    }

    return {
      sentCount,
      totalUsers: userIds.length,
      results
    };
  }

  /**
   * Handle invalid tokens by deactivating them
   * @param {string} userId - User ID
   * @param {Array<string>} tokens - FCM tokens
   * @param {Array<Object>} results - Send results
   */
  async handleInvalidTokens(userId, tokens, results) {
    const invalidTokens = results
      .filter(r => !r.success && this.isTokenError(r.errorCode))
      .map(r => r.token);

    if (invalidTokens.length > 0) {
      try {
        const user = await User.findById(userId);
        if (user) {
          for (const token of invalidTokens) {
            await user.deactivateFCMToken(token);
          }
          Logger.info('Deactivated invalid FCM tokens', {
            userId,
            invalidTokensCount: invalidTokens.length
          });
        }
      } catch (error) {
        Logger.error('Failed to deactivate invalid tokens', {
          userId,
          error: error.message
        });
      }
    }
  }

  /**
   * Check if error code indicates invalid token
   * @param {string} errorCode - Error code
   * @returns {boolean}
   */
  isTokenError(errorCode) {
    const tokenErrors = [
      'messaging/invalid-registration-token',
      'messaging/registration-token-not-registered',
      'messaging/invalid-argument'
    ];
    return tokenErrors.includes(errorCode);
  }

  /**
   * Check if notification should be sent based on user preferences
   * @param {Object} user - User object
   * @param {string} notificationType - Type of notification
   * @returns {boolean}
   */
  shouldSendNotification(user, notificationType) {
    if (!notificationType || !user.notificationPreferences) {
      return true;
    }

    const preferences = user.notificationPreferences;

    switch (notificationType) {
      case 'new_message':
        return preferences.newMessages;
      case 'new_match':
        return preferences.newMatches;
      case 'super_message':
        return preferences.superMessages;
      case 'promotion':
        return preferences.promotions;
      case 'system_update':
        return preferences.systemUpdates;
      default:
        return true;
    }
  }

  /**
   * Check if current time is within user's quiet hours
   * @param {Object} user - User object
   * @returns {boolean}
   */
  isQuietHours(user) {
    if (!user.notificationPreferences?.quietHours?.enabled) {
      return false;
    }

    try {
      const quietHours = user.notificationPreferences.quietHours;
      const timezone = quietHours.timezone || 'Asia/Kolkata';

      const now = new Date();
      const userTime = new Date(now.toLocaleString("en-US", { timeZone: timezone }));

      const currentHour = userTime.getHours();
      const currentMinute = userTime.getMinutes();
      const currentTime = currentHour * 60 + currentMinute;

      const [startHour, startMinute] = quietHours.startTime.split(':').map(Number);
      const [endHour, endMinute] = quietHours.endTime.split(':').map(Number);

      const startTime = startHour * 60 + startMinute;
      const endTime = endHour * 60 + endMinute;

      // Handle overnight quiet hours (e.g., 22:00 to 08:00)
      if (startTime > endTime) {
        return currentTime >= startTime || currentTime <= endTime;
      } else {
        return currentTime >= startTime && currentTime <= endTime;
      }
    } catch (error) {
      Logger.error('Error checking quiet hours', {
        userId: user._id,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Clean up inactive tokens (older than 30 days)
   * @returns {Promise<Object>}
   */
  async cleanupInactiveTokens() {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const result = await User.updateMany(
        {},
        {
          $pull: {
            fcmTokens: {
              $or: [
                { isActive: false },
                { lastUsed: { $lt: thirtyDaysAgo } }
              ]
            }
          }
        }
      );

      Logger.info('Cleaned up inactive FCM tokens', {
        modifiedCount: result.modifiedCount
      });

      return {
        success: true,
        modifiedCount: result.modifiedCount
      };
    } catch (error) {
      Logger.error('Failed to cleanup inactive tokens', {
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Get FCM statistics
   * @returns {Promise<Object>}
   */
  async getStatistics() {
    try {
      const stats = await User.aggregate([
        {
          $project: {
            totalTokens: { $size: '$fcmTokens' },
            activeTokens: {
              $size: {
                $filter: {
                  input: '$fcmTokens',
                  cond: { $eq: ['$$this.isActive', true] }
                }
              }
            },
            platforms: '$fcmTokens.platform'
          }
        },
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            usersWithTokens: {
              $sum: { $cond: [{ $gt: ['$totalTokens', 0] }, 1, 0] }
            },
            totalTokens: { $sum: '$totalTokens' },
            activeTokens: { $sum: '$activeTokens' },
            allPlatforms: { $push: '$platforms' }
          }
        },
        {
          $project: {
            _id: 0,
            totalUsers: 1,
            usersWithTokens: 1,
            totalTokens: 1,
            activeTokens: 1,
            platforms: {
              $reduce: {
                input: '$allPlatforms',
                initialValue: [],
                in: { $concatArrays: ['$$value', '$$this'] }
              }
            }
          }
        }
      ]);

      const result = stats[0] || {
        totalUsers: 0,
        usersWithTokens: 0,
        totalTokens: 0,
        activeTokens: 0,
        platforms: []
      };

      // Count platforms
      const platformCounts = {};
      result.platforms.forEach(platform => {
        platformCounts[platform] = (platformCounts[platform] || 0) + 1;
      });

      return {
        success: true,
        statistics: {
          ...result,
          platformCounts,
          platforms: undefined
        }
      };
    } catch (error) {
      Logger.error('Failed to get FCM statistics', {
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
module.exports = new FCMService();
