const mongoose = require('mongoose');
const PlanConfig = require('./models/planConfig');
require('dotenv').config();

async function checkDatabase() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB:', process.env.MONGO_URI);

    // Check plan configurations
    console.log('\n📋 Checking Plan Configurations...');
    const plans = await PlanConfig.find({});
    console.log(`Found ${plans.length} plans in database:`);
    
    if (plans.length === 0) {
      console.log('❌ No plans found! Seeding plans...');
      
      const planData = [
        {
          plan: 'monthly',
          label: 'Monthly Premium',
          price: 99,
          priceAfterTax: 117,
          strikePrice: 149,
          trialPrice: 0,
          freeTrial: false,
          planId: 'plan_QkkDaTp9Hje6uC',
          validityInDays: 30,
          swipeLimitPerDay: 50,
          superMessageTokensPerMonth: 10,
          allowDirectMessage: true,
          autoRenew: true
        },
        {
          plan: 'yearly',
          label: 'Yearly Premium',
          price: 499,
          priceAfterTax: 589,
          strikePrice: 1188,
          trialPrice: 0,
          freeTrial: false,
          planId: 'plan_QkkDw9QRHFT0nG',
          validityInDays: 365,
          swipeLimitPerDay: 50,
          superMessageTokensPerMonth: 10,
          allowDirectMessage: true,
          autoRenew: true
        }
      ];

      for (const plan of planData) {
        const createdPlan = await PlanConfig.create(plan);
        console.log(`✅ Created plan: ${createdPlan.label} (${createdPlan.planId})`);
      }
    } else {
      plans.forEach(plan => {
        console.log(`✅ ${plan.label}: ${plan.planId} - ₹${plan.priceAfterTax}`);
      });
    }

    // Check collections
    console.log('\n📊 Database Collections:');
    const collections = await mongoose.connection.db.listCollections().toArray();
    collections.forEach(col => {
      console.log(`- ${col.name}`);
    });

    console.log('\n🎯 Database check completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  checkDatabase();
}

module.exports = checkDatabase;
