// const Match = require('../models/match');

// /**
//  * Ensures a match exists between two users. If not, creates it.
//  * - Always sorts user IDs to ensure uniqueness
//  * - Returns the match document
//  */
// const ensureMatchExists = async (userId1, userId2) => {
//   const sorted = [userId1, userId2].sort(); // Enforce consistent order
//   let match = await Match.findOne({ users: sorted });
//   if (!match) {
//     match = await Match.create({ users: sorted });
//   }
//   return match;
// };

// module.exports = ensureMatchExists;


const Match = require('../models/match');
const fcmService = require('../services/fcmService');
const notificationTemplates = require('../services/notificationTemplates');
const User = require('../models/user');
const Logger = require('./logger');

/**
 * Ensures a match exists between two users. If not, creates it.
 * - Always sorts user IDs to ensure uniqueness
 * - Sends push notifications for new matches
 */
const ensureMatchExists = async (userId1, userId2) => {
  const sorted = [userId1.toString(), userId2.toString()].sort();
  let match = await Match.findOne({ users: sorted });

  if (!match) {
    match = await Match.create({ users: sorted });

    // Send match notifications to both users
    await sendMatchNotifications(userId1, userId2, match._id);
  }

  return match;
};

/**
 * Send push notifications for new match
 * @param {string} userId1 - First user ID
 * @param {string} userId2 - Second user ID
 * @param {string} matchId - Match ID
 */
const sendMatchNotifications = async (userId1, userId2, matchId) => {
  try {
    // Get both users' information
    const [user1, user2] = await Promise.all([
      User.findById(userId1).select('nickname'),
      User.findById(userId2).select('nickname')
    ]);

    if (!user1 || !user2) {
      Logger.warn('Users not found for match notification', { userId1, userId2 });
      return;
    }

    // Send notification to both users
    const notifications = [
      {
        recipientId: userId1,
        data: {
          matchedUserName: user2.nickname || 'Someone',
          matchId: matchId.toString(),
          matchedUserId: userId2
        }
      },
      {
        recipientId: userId2,
        data: {
          matchedUserName: user1.nickname || 'Someone',
          matchId: matchId.toString(),
          matchedUserId: userId1
        }
      }
    ];

    for (const notif of notifications) {
      const template = notificationTemplates.getNewMatchTemplate(notif.data);

      const result = await fcmService.sendToUser(
        notif.recipientId,
        template.notification,
        template.data,
        template.options
      );

      if (result.success) {
        Logger.info('Match push notification sent', {
          recipientUserId: notif.recipientId,
          matchId,
          sentCount: result.sentCount
        });
      } else {
        Logger.warn('Failed to send match push notification', {
          recipientUserId: notif.recipientId,
          matchId,
          error: result.error
        });
      }
    }

  } catch (error) {
    Logger.error('Error sending match push notification', {
      userId1,
      userId2,
      matchId,
      error: error.message,
      stack: error.stack
    });
  }
};

module.exports = ensureMatchExists;
