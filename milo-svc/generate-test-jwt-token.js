const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
require('dotenv').config();

// Test user data
const testUser = {
  userId: '686f99e04af68867eda342b2', // Example user ID
  phone: '+919876543210',
  email: '<EMAIL>'
};

// Generate JWT token
function generateTestToken(userId, expiresIn = '24h') {
  const payload = {
    userId: userId,
    iat: Math.floor(Date.now() / 1000)
  };

  const token = jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
  return token;
}

// Main function
async function main() {
  try {
    console.log('🔐 Generating Test JWT Token for Milo-SVC API Testing');
    console.log('=' .repeat(60));

    const token = generateTestToken(testUser.userId);

    console.log('Test User Details:');
    console.log(`User ID: ${testUser.userId}`);
    console.log(`Phone: ${testUser.phone}`);
    console.log(`Email: ${testUser.email}`);
    console.log('');

    console.log('Generated JWT Token:');
    console.log(token);
    console.log('');

    console.log('Token expires in: 24 hours');
    console.log('');

    console.log('Usage in cURL:');
    console.log(`curl -H "Authorization: Bearer ${token}" ...`);
    console.log('');

    console.log('🧪 Test Commands:');
    console.log('');

    // Get plans
    console.log('1. Get Subscription Plans:');
    console.log(`curl -X GET "https://api.miloapp.in/subscription/plans" \\`);
    console.log(`  -H "Content-Type: application/json"`);
    console.log('');

    // Create monthly subscription
    console.log('2. Create Monthly Recurring Subscription:');
    console.log(`curl -X POST "https://api.miloapp.in/subscription/create-order" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -d '{`);
    console.log(`    "planId": "plan_QkkDaTp9Hje6uC",`);
    console.log(`    "recurring": true`);
    console.log(`  }'`);
    console.log('');

    // Create yearly subscription
    console.log('3. Create Yearly Recurring Subscription:');
    console.log(`curl -X POST "https://api.miloapp.in/subscription/create-order" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -d '{`);
    console.log(`    "planId": "plan_QkkDw9QRHFT0nG",`);
    console.log(`    "recurring": true`);
    console.log(`  }'`);
    console.log('');

    // Create one-time order
    console.log('4. Create Monthly One-Time Order:');
    console.log(`curl -X POST "https://api.miloapp.in/subscription/create-order" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}" \\`);
    console.log(`  -d '{`);
    console.log(`    "planId": "plan_QkkDaTp9Hje6uC",`);
    console.log(`    "recurring": false`);
    console.log(`  }'`);
    console.log('');

    // Get subscription status
    console.log('5. Get Subscription Status:');
    console.log(`curl -X GET "https://api.miloapp.in/subscription/status" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${token}"`);
    console.log('');

    console.log('📝 Note: Replace the User ID in this script with an actual user ID from your database for real testing.');
    console.log('');
    console.log('🔍 To verify token payload:');
    console.log('Visit: https://jwt.io and paste the token to decode it.');

  } catch (error) {
    console.error('Error generating token:', error);
  }
}

// Run the script
if (require.main === module) {
  main();
}