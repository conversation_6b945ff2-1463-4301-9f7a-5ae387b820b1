# Test script for updated subscription API with planId

$API_BASE = "http://localhost:3001/api"
$TOKEN = "your_jwt_token_here"  # Replace with actual token

Write-Host "=== Testing Updated Subscription API with planId ===" -ForegroundColor Green
Write-Host ""

Write-Host "1. Testing GET /subscription/plans" -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $TOKEN"
    "Content-Type" = "application/json"
}

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/plans" -Method GET -Headers $headers
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "2. Testing POST /subscription/create-payment with Monthly Plan" -ForegroundColor Yellow
$monthlyBody = @{
    planId = "plan_QwQVKjofv1kEIX"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/create-payment" -Method POST -Headers $headers -Body $monthlyBody
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. Testing POST /subscription/create-payment with Yearly Plan" -ForegroundColor Yellow
$yearlyBody = @{
    planId = "plan_QwQVupm00bZ4d9"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/create-payment" -Method POST -Headers $headers -Body $yearlyBody
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. Testing with Invalid planId (should fail)" -ForegroundColor Yellow
$invalidBody = @{
    planId = "invalid_plan_id"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/create-payment" -Method POST -Headers $headers -Body $invalidBody
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "5. Testing with missing planId (should fail)" -ForegroundColor Yellow
$emptyBody = @{} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_BASE/subscription/create-payment" -Method POST -Headers $headers -Body $emptyBody
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}
