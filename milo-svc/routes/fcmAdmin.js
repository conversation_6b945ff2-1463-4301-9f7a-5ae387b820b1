/**
 * FCM Admin Routes
 * 
 * This file defines admin-level routes for FCM operations including:
 * - Broadcast notifications
 * - Topic management
 * - Analytics and statistics
 * - Token cleanup
 * 
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const fcmAdminController = require('../controllers/fcmAdminController');
const { verifyToken } = require('../middleware/authMiddleware');
const { authRateLimit } = require('../middleware/rateLimiting');

// Apply authentication middleware to all admin routes
router.use(verifyToken);

// Apply rate limiting to all admin routes
router.use(authRateLimit);

// TODO: Add admin role verification middleware
// router.use(verifyAdminRole);

// ==================== BROADCAST NOTIFICATIONS ====================

/**
 * Send broadcast notification to all users or specific users
 * POST /fcm/admin/broadcast
 * 
 * Body:
 * {
 *   "title": "Notification Title",
 *   "body": "Notification Body",
 *   "data": {
 *     "key": "value"
 *   },
 *   "notificationType": "system_update",
 *   "targetUsers": ["userId1", "userId2"] // Optional, if not provided sends to all
 * }
 */
router.post('/broadcast', fcmAdminController.sendBroadcast);

/**
 * Send notification to a topic
 * POST /fcm/admin/topic
 * 
 * Body:
 * {
 *   "topic": "topic_name",
 *   "title": "Notification Title",
 *   "body": "Notification Body",
 *   "data": {
 *     "key": "value"
 *   },
 *   "notificationType": "system_update"
 * }
 */
router.post('/topic', fcmAdminController.sendTopicNotification);

/**
 * Send notification using predefined template
 * POST /fcm/admin/template
 * 
 * Body:
 * {
 *   "templateType": "promotion|system_update|subscription_expiry",
 *   "templateData": {
 *     "title": "Custom Title",
 *     "message": "Custom Message",
 *     "imageUrl": "https://example.com/image.jpg"
 *   },
 *   "targetUsers": ["userId1", "userId2"], // Optional
 *   "language": "en" // Optional, defaults to 'en'
 * }
 */
router.post('/template', fcmAdminController.sendTemplateNotification);

// ==================== ANALYTICS AND STATISTICS ====================

/**
 * Get FCM statistics
 * GET /fcm/admin/stats
 * 
 * Returns:
 * - Total users
 * - Users with FCM tokens
 * - Total tokens
 * - Active tokens
 * - Platform distribution
 */
router.get('/stats', fcmAdminController.getStatistics);

/**
 * Get users with FCM tokens
 * GET /fcm/admin/users
 * 
 * Query parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 50)
 * - platform: Filter by platform (ios|android|web)
 * - active: Filter by active status (true|false)
 */
router.get('/users', fcmAdminController.getUsersWithTokens);

// ==================== MAINTENANCE ====================

/**
 * Clean up inactive FCM tokens
 * POST /fcm/admin/cleanup
 * 
 * Removes tokens that are:
 * - Marked as inactive
 * - Not used for more than 30 days
 */
router.post('/cleanup', fcmAdminController.cleanupTokens);

module.exports = router;
