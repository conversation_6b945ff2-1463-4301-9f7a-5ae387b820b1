/**
 * FCM (Firebase Cloud Messaging) Routes
 * 
 * This file defines all routes related to FCM operations including:
 * - Token management
 * - Notification preferences
 * - Topic subscriptions
 * - Test notifications
 * 
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const fcmController = require('../controllers/fcmController');
const { verifyToken } = require('../middleware/authMiddleware');
const { authRateLimit } = require('../middleware/rateLimiting');

// Apply authentication middleware to all FCM routes
router.use(verifyToken);

// Apply rate limiting to all FCM routes
router.use(authRateLimit);

// ==================== TOKEN MANAGEMENT ====================

/**
 * Register FCM token
 * POST /fcm/token/register
 * 
 * Body:
 * {
 *   "token": "FCM_TOKEN_STRING",
 *   "deviceId": "UNIQUE_DEVICE_ID",
 *   "platform": "ios|android|web",
 *   "appVersion": "1.0.0" (optional)
 * }
 */
router.post('/token/register', fcmController.registerToken);

/**
 * Update FCM token activity
 * PUT /fcm/token/activity
 * 
 * Body:
 * {
 *   "token": "FCM_TOKEN_STRING"
 * }
 */
router.put('/token/activity', fcmController.updateTokenActivity);

/**
 * Remove FCM token
 * DELETE /fcm/token/:deviceId
 */
router.delete('/token/:deviceId', fcmController.removeToken);

/**
 * Get user's FCM tokens
 * GET /fcm/tokens
 */
router.get('/tokens', fcmController.getTokens);

// ==================== NOTIFICATION PREFERENCES ====================

/**
 * Update notification preferences
 * PUT /fcm/preferences
 * 
 * Body:
 * {
 *   "preferences": {
 *     "newMessages": true,
 *     "newMatches": true,
 *     "superMessages": true,
 *     "promotions": false,
 *     "systemUpdates": true,
 *     "quietHours": {
 *       "enabled": true,
 *       "startTime": "22:00",
 *       "endTime": "08:00",
 *       "timezone": "Asia/Kolkata"
 *     }
 *   }
 * }
 */
router.put('/preferences', fcmController.updatePreferences);

/**
 * Get notification preferences
 * GET /fcm/preferences
 */
router.get('/preferences', fcmController.getPreferences);

// ==================== TOPIC MANAGEMENT ====================

/**
 * Subscribe to topic
 * POST /fcm/topic/subscribe
 * 
 * Body:
 * {
 *   "topic": "topic_name"
 * }
 */
router.post('/topic/subscribe', fcmController.subscribeToTopic);

/**
 * Unsubscribe from topic
 * POST /fcm/topic/unsubscribe
 * 
 * Body:
 * {
 *   "topic": "topic_name"
 * }
 */
router.post('/topic/unsubscribe', fcmController.unsubscribeFromTopic);

// ==================== TESTING ====================

/**
 * Send test notification
 * POST /fcm/test
 * 
 * Body:
 * {
 *   "type": "custom",
 *   "title": "Test Notification",
 *   "body": "This is a test notification",
 *   "data": {
 *     "key": "value"
 *   }
 * }
 */
router.post('/test', fcmController.sendTestNotification);

module.exports = router;
