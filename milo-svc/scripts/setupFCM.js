/**
 * FCM Setup Script
 * 
 * This script helps set up and validate FCM configuration
 * Run this script after setting up your environment variables
 * 
 * Usage: node scripts/setupFCM.js
 * 
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

const mongoose = require('mongoose');
const fcmService = require('../services/fcmService');
const fcmErrorHandler = require('../middleware/fcmErrorHandler');
const User = require('../models/user');
const Logger = require('../utils/logger');
require('dotenv').config();

class FCMSetup {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
  }

  /**
   * Run the complete FCM setup process
   */
  async run() {
    console.log('🚀 Starting FCM Setup and Validation...\n');

    try {
      await this.connectDatabase();
      await this.validateEnvironment();
      await this.validateFirebaseConfig();
      await this.validateFCMService();
      await this.createIndexes();
      await this.runHealthChecks();
      await this.displayResults();
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Connect to database
   */
  async connectDatabase() {
    try {
      const dbUrl = process.env.DATABASE_URL || process.env.MONGODB_URI || 'mongodb://localhost:27017/polo';
      await mongoose.connect(dbUrl);
      this.success.push('Database connection established');
      console.log('✅ Database connected');
    } catch (error) {
      this.errors.push(`Database connection failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate environment variables
   */
  async validateEnvironment() {
    console.log('🔍 Validating environment variables...');

    const requiredVars = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_CLIENT_EMAIL',
      'FIREBASE_PRIVATE_KEY'
    ];

    const optionalVars = [
      'FCM_MAX_RETRIES',
      'FCM_RETRY_DELAY',
      'FCM_BATCH_SIZE',
      'DEFAULT_NOTIFICATION_LANGUAGE'
    ];

    // Check required variables
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        this.errors.push(`Missing required environment variable: ${varName}`);
      } else {
        this.success.push(`Environment variable ${varName} is set`);
      }
    }

    // Check optional variables
    for (const varName of optionalVars) {
      if (!process.env[varName]) {
        this.warnings.push(`Optional environment variable ${varName} not set, using default`);
      } else {
        this.success.push(`Environment variable ${varName} is set`);
      }
    }

    // Validate Firebase private key format
    if (process.env.FIREBASE_PRIVATE_KEY) {
      const privateKey = process.env.FIREBASE_PRIVATE_KEY;
      if (!privateKey.includes('BEGIN PRIVATE KEY') || !privateKey.includes('END PRIVATE KEY')) {
        this.errors.push('FIREBASE_PRIVATE_KEY appears to be in wrong format');
      } else {
        this.success.push('FIREBASE_PRIVATE_KEY format is valid');
      }
    }

    console.log('✅ Environment validation completed');
  }

  /**
   * Validate Firebase configuration
   */
  async validateFirebaseConfig() {
    console.log('🔍 Validating Firebase configuration...');

    try {
      const validation = fcmErrorHandler.validateConfiguration();
      
      if (validation.valid) {
        this.success.push('Firebase Admin SDK initialized successfully');
        console.log('✅ Firebase configuration is valid');
      } else {
        validation.errors.forEach(error => {
          this.errors.push(`Firebase configuration error: ${error}`);
        });
      }
    } catch (error) {
      this.errors.push(`Firebase validation failed: ${error.message}`);
    }
  }

  /**
   * Validate FCM service
   */
  async validateFCMService() {
    console.log('🔍 Validating FCM service...');

    try {
      if (fcmService.isAvailable()) {
        this.success.push('FCM service is available');
        console.log('✅ FCM service is available');

        // Test token validation (with a dummy token)
        const testToken = 'dummy-token-for-validation-test';
        try {
          await fcmService.validateToken(testToken);
          // This should fail, but we're testing the method works
        } catch (error) {
          // Expected to fail with dummy token
          this.success.push('FCM token validation method is working');
        }
      } else {
        this.errors.push('FCM service is not available');
      }
    } catch (error) {
      this.errors.push(`FCM service validation failed: ${error.message}`);
    }
  }

  /**
   * Create necessary database indexes
   */
  async createIndexes() {
    console.log('🔍 Creating database indexes...');

    try {
      // Create indexes for FCM tokens
      await User.collection.createIndex({ 'fcmTokens.token': 1 });
      await User.collection.createIndex({ 'fcmTokens.deviceId': 1 });
      await User.collection.createIndex({ 'fcmTokens.isActive': 1 });
      
      this.success.push('FCM database indexes created');
      console.log('✅ Database indexes created');
    } catch (error) {
      this.warnings.push(`Index creation warning: ${error.message}`);
    }
  }

  /**
   * Run health checks
   */
  async runHealthChecks() {
    console.log('🔍 Running health checks...');

    try {
      // Check database connection
      await mongoose.connection.db.admin().ping();
      this.success.push('Database health check passed');

      // Check FCM statistics
      const stats = await fcmService.getStatistics();
      if (stats.success) {
        this.success.push('FCM statistics retrieval working');
        console.log(`📊 Current FCM stats: ${stats.statistics.totalUsers} users, ${stats.statistics.activeTokens} active tokens`);
      } else {
        this.warnings.push('FCM statistics retrieval failed');
      }

      console.log('✅ Health checks completed');
    } catch (error) {
      this.warnings.push(`Health check warning: ${error.message}`);
    }
  }

  /**
   * Display setup results
   */
  async displayResults() {
    console.log('\n📋 FCM Setup Results:');
    console.log('='.repeat(50));

    if (this.success.length > 0) {
      console.log('\n✅ Successful items:');
      this.success.forEach(item => console.log(`  • ${item}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      this.warnings.forEach(item => console.log(`  • ${item}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(item => console.log(`  • ${item}`));
    }

    console.log('\n' + '='.repeat(50));

    if (this.errors.length === 0) {
      console.log('🎉 FCM setup completed successfully!');
      console.log('\n📚 Next steps:');
      console.log('  1. Test FCM functionality with the test endpoints');
      console.log('  2. Configure notification templates as needed');
      console.log('  3. Set up monitoring and alerts');
      console.log('  4. Review security configurations');
      console.log('  5. Deploy to staging for testing');
    } else {
      console.log('❌ FCM setup completed with errors. Please fix the errors above.');
      process.exit(1);
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      await mongoose.disconnect();
      console.log('\n🧹 Cleanup completed');
    } catch (error) {
      console.error('Cleanup error:', error.message);
    }
  }

  /**
   * Create sample data for testing
   */
  async createSampleData() {
    console.log('🔍 Creating sample data for testing...');

    try {
      // Create a test user with FCM token
      const testUser = await User.findOneAndUpdate(
        { email: '<EMAIL>' },
        {
          email: '<EMAIL>',
          nickname: 'FCM Test User',
          isOnboarded: true,
          location: { type: 'Point', coordinates: [0, 0] },
          fcmTokens: [{
            token: 'test-fcm-token-' + Date.now(),
            deviceId: 'test-device-123',
            platform: 'android',
            appVersion: '1.0.0',
            isActive: true,
            lastUsed: new Date(),
            createdAt: new Date()
          }]
        },
        { upsert: true, new: true }
      );

      this.success.push(`Test user created with ID: ${testUser._id}`);
      console.log('✅ Sample data created');
    } catch (error) {
      this.warnings.push(`Sample data creation failed: ${error.message}`);
    }
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  const setup = new FCMSetup();
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  
  if (args.includes('--with-sample-data')) {
    setup.createSampleData = async function() {
      await this.createSampleData();
    };
  }

  setup.run().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = FCMSetup;
