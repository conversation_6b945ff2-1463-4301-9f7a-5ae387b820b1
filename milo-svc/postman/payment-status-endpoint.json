{"name": "Payment Status - Check and Activate", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"paymentId\": \"{{payment_id}}\"\n}"}, "url": {"raw": "{{base_url}}/subscription/payment-status", "host": ["{{base_url}}"], "path": ["subscription", "payment-status"]}, "description": "Check payment status and activate premium subscription if payment is successful.\n\n**Request Body Options:**\n\n1. Using internal payment ID:\n```json\n{\n  \"paymentId\": \"60d21b4667d0d8992e610c85\"\n}\n```\n\n2. Using Razorpay payment ID:\n```json\n{\n  \"razorpayPaymentId\": \"pay_ABCD1234567890\"\n}\n```\n\n3. Using Razorpay order ID:\n```json\n{\n  \"razorpayOrderId\": \"order_ABCD1234567890\"\n}\n```\n\n**Response:**\n- If payment successful: Activates premium and returns subscription details\n- If payment pending: Returns current payment status\n- Handles both microservice and direct Razorpay payments"}, "response": [{"name": "Payment Successful - Subscription Activated", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"paymentId\": \"60d21b4667d0d8992e610c85\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/subscription/payment-status", "host": ["{{base_url}}"], "path": ["subscription", "payment-status"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"paymentStatus\": \"captured\",\n    \"subscriptionActive\": true,\n    \"subscription\": {\n      \"id\": \"60d21b4667d0d8992e610c86\",\n      \"plan\": \"super\",\n      \"startDate\": \"2025-07-24T10:30:00.000Z\",\n      \"endDate\": \"2026-07-24T10:30:00.000Z\"\n    }\n  },\n  \"message\": \"Payment successful and subscription activated\"\n}"}, {"name": "Payment Pending", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"razorpayPaymentId\": \"pay_ABCD1234567890\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/subscription/payment-status", "host": ["{{base_url}}"], "path": ["subscription", "payment-status"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"paymentStatus\": \"created\",\n    \"subscriptionActive\": false,\n    \"payment\": {\n      \"id\": \"60d21b4667d0d8992e610c85\",\n      \"amount\": 999,\n      \"currency\": \"INR\",\n      \"planType\": \"yearly\",\n      \"status\": \"created\",\n      \"createdAt\": \"2025-07-24T10:00:00.000Z\"\n    }\n  },\n  \"message\": \"Payment status: created\"\n}"}]}