#!/usr/bin/env node

/**
 * Delete Specific Users Script
 * 
 * This script deletes specified users from both:
 * 1. Seekho Backend database (users and their subscriptions)
 * 2. Payment Microservice database (subscriptions and related data)
 */

const { MongoClient } = require('mongodb');

// Database configurations
const SEEKHO_DB_URI = 'mongodb+srv://prav59632:<EMAIL>/';
const PAYMENT_DB_URI = 'mongodb+srv://pragya:<EMAIL>/payment-service?retryWrites=true&w=majority';

// Users to delete
const USERS_TO_DELETE = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

console.log('🗑️  User Deletion Script');
console.log('=' .repeat(50));
console.log('Users to delete:');
USERS_TO_DELETE.forEach((email, index) => {
  console.log(`${index + 1}. ${email}`);
});
console.log('');

async function deleteFromSeekhoBackend() {
  console.log('🔍 Connecting to Seekho Backend database...');
  
  const client = new MongoClient(SEEKHO_DB_URI);
  
  try {
    await client.connect();
    const db = client.db(); // Uses default database from URI
    
    console.log('✅ Connected to Seekho Backend database');
    
    // Find users to delete
    const usersCollection = db.collection('users');
    const subscriptionsCollection = db.collection('subscriptions');
    
    for (const email of USERS_TO_DELETE) {
      console.log(`\n🔍 Processing user: ${email}`);
      
      // Find user
      const user = await usersCollection.findOne({ email: email });
      
      if (user) {
        console.log(`  ✅ Found user: ${user._id}`);
        
        // Find and delete user's subscriptions
        const userSubscriptions = await subscriptionsCollection.find({ user: user._id }).toArray();
        
        if (userSubscriptions.length > 0) {
          console.log(`  📦 Found ${userSubscriptions.length} subscription(s)`);
          
          const deleteSubResult = await subscriptionsCollection.deleteMany({ user: user._id });
          console.log(`  🗑️  Deleted ${deleteSubResult.deletedCount} subscription(s)`);
        } else {
          console.log(`  📦 No subscriptions found`);
        }
        
        // Delete user
        const deleteUserResult = await usersCollection.deleteOne({ _id: user._id });
        console.log(`  🗑️  Deleted user: ${deleteUserResult.deletedCount > 0 ? 'SUCCESS' : 'FAILED'}`);
        
      } else {
        console.log(`  ❌ User not found in Seekho Backend`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error in Seekho Backend deletion:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from Seekho Backend database');
  }
}

async function deleteFromPaymentMicroservice() {
  console.log('\n🔍 Connecting to Payment Microservice database...');
  
  const client = new MongoClient(PAYMENT_DB_URI);
  
  try {
    await client.connect();
    const db = client.db();
    
    console.log('✅ Connected to Payment Microservice database');
    
    const subscriptionsCollection = db.collection('subscriptions');
    const ordersCollection = db.collection('orders');
    
    for (const email of USERS_TO_DELETE) {
      console.log(`\n🔍 Processing user: ${email}`);
      
      // Find subscriptions by email in paymentContext
      const subscriptions = await subscriptionsCollection.find({
        'paymentContext.metadata.userEmail': email
      }).toArray();
      
      if (subscriptions.length > 0) {
        console.log(`  📦 Found ${subscriptions.length} subscription(s) in payment microservice`);
        
        // Delete subscriptions
        const deleteSubResult = await subscriptionsCollection.deleteMany({
          'paymentContext.metadata.userEmail': email
        });
        console.log(`  🗑️  Deleted ${deleteSubResult.deletedCount} subscription(s)`);
      } else {
        console.log(`  📦 No subscriptions found in payment microservice`);
      }
      
      // Find and delete orders by email
      const orders = await ordersCollection.find({
        'paymentContext.metadata.userEmail': email
      }).toArray();
      
      if (orders.length > 0) {
        console.log(`  📋 Found ${orders.length} order(s) in payment microservice`);
        
        const deleteOrderResult = await ordersCollection.deleteMany({
          'paymentContext.metadata.userEmail': email
        });
        console.log(`  🗑️  Deleted ${deleteOrderResult.deletedCount} order(s)`);
      } else {
        console.log(`  📋 No orders found in payment microservice`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error in Payment Microservice deletion:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from Payment Microservice database');
  }
}

async function runDeletion() {
  console.log('🚀 Starting user deletion process...\n');
  
  // Delete from Seekho Backend
  await deleteFromSeekhoBackend();
  
  // Delete from Payment Microservice
  await deleteFromPaymentMicroservice();
  
  console.log('\n✅ User deletion process completed!');
  console.log('📝 Summary: All specified users and their associated data have been processed.');
}

// Confirmation prompt
function askForConfirmation() {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question('⚠️  Are you sure you want to delete these users? This action cannot be undone. (yes/no): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

// Main execution
if (require.main === module) {
  askForConfirmation()
    .then(confirmed => {
      if (confirmed) {
        return runDeletion();
      } else {
        console.log('❌ User deletion cancelled.');
        process.exit(0);
      }
    })
    .then(() => {
      console.log('🎯 Script completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Script execution failed:', error);
      process.exit(1);
    });
}

module.exports = { runDeletion };
