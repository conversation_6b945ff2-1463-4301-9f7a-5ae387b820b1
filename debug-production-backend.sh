#!/bin/bash

echo "🔍 Production Backend Diagnostic"
echo "============================================"

PRODUCTION_URL="https://learner.netaapp.in"

echo ""
echo "1. Testing Backend Health"
curl -s "$PRODUCTION_URL/api/health" | jq '.' || echo "❌ Health endpoint not available"

echo ""
echo "2. Testing Payment Microservice Integration"
# This might not be exposed, but worth trying
curl -s "$PRODUCTION_URL/api/payment/health" | jq '.' || echo "❌ Payment microservice health not available"

echo ""
echo "3. Testing Environment Info (if available)"
curl -s "$PRODUCTION_URL/api/config" | jq '.' || echo "❌ Config endpoint not available"

echo ""
echo "4. Manual cURL Commands for You to Run:"
echo "============================================"

# Generate fresh token
cat > generate_token.js << 'EOF'
const jwt = require('jsonwebtoken');
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

const tokenPayload = {
  id: '688866c9baa54c07f2616aaf',
  email: '<EMAIL>',
  name: 'Praveen Singh',
  packageId: 'com.gumbo.learning',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
};

const token = jwt.sign(tokenPayload, JWT_SECRET);
console.log(token);
EOF

JWT_TOKEN=$(node generate_token.js)

echo ""
echo "🔧 COPY AND RUN THESE COMMANDS MANUALLY:"
echo ""

echo "# Test 1: Check if subscription creation works with verbose output"
echo "curl -v -X POST '$PRODUCTION_URL/api/subscriptions/create-order' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer $JWT_TOKEN' \\"
echo "  -H 'x-package-id: com.gumbo.learning' \\"
echo "  -d '{"
echo "    \"plan\": \"monthly\","
echo "    \"recurring\": true,"
echo "    \"name\": \"Praveen Singh\","
echo "    \"email\": \"<EMAIL>\","
echo "    \"phone\": \"**********\""
echo "  }'"

echo ""
echo "# Test 2: Check backend logs (if you have access)"
echo "# Look for errors around the time you run the above command"

echo ""
echo "# Test 3: Check if payment microservice is accessible from production"
echo "# (Run this on your production server)"
echo "curl -s http://localhost:3000/api/health || curl -s https://payments.netaapp.in/api/health"

echo ""
echo "🔍 DEBUGGING CHECKLIST:"
echo "============================================"
echo "□ 1. Verify production backend is using correct payment microservice URL"
echo "□ 2. Check production environment variables match local .env"
echo "□ 3. Verify payment microservice is running and accessible from production"
echo "□ 4. Check production backend logs during subscription creation"
echo "□ 5. Verify database connections in production"
echo "□ 6. Check if all code changes are actually deployed"

echo ""
echo "🚨 MOST LIKELY ISSUES:"
echo "============================================"
echo "1. Production backend still pointing to old payment microservice URL"
echo "2. Payment microservice not running in production"
echo "3. Environment variables not updated in production"
echo "4. Code not fully deployed to production"

echo ""
echo "💡 QUICK FIXES TO TRY:"
echo "============================================"
echo "1. Restart production backend service"
echo "2. Restart payment microservice in production"
echo "3. Verify environment variables in production"
echo "4. Check production deployment logs"

# Cleanup
rm -f generate_token.js

echo ""
echo "🎯 Run the manual curl command above and check the verbose output!"
