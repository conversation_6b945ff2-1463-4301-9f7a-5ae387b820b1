#!/bin/bash

# Production Subscription Creation Test - cURL Commands
# This script tests the exact same flow that the Flutter app uses

echo "🧪 Testing Production Subscription Creation with cURL"
echo "============================================================"

# Configuration
PRODUCTION_URL="https://learner.netaapp.in"
PACKAGE_ID="com.gumbo.learning"

# <PERSON>rave<PERSON>'s user data (from database)
USER_ID="688866c9baa54c07f2616aaf"
USER_EMAIL="<EMAIL>"
USER_NAME="Praveen Singh"

# Generate JWT token for <PERSON>rave<PERSON> (using the correct format)
echo "🔑 Step 1: Generating JWT token for Praveen..."

# You'll need to run this Node.js command to generate the token
cat > generate_token.js << 'EOF'
const jwt = require('jsonwebtoken');
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

const tokenPayload = {
  id: '688866c9baa54c07f2616aaf', // <PERSON><PERSON><PERSON>'s user ID
  email: '<EMAIL>',
  name: 'Praveen Singh',
  packageId: 'com.gumbo.learning',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
};

const token = jwt.sign(tokenPayload, JWT_SECRET);
console.log(token);
EOF

# Generate the token
JWT_TOKEN=$(node generate_token.js)
echo "✅ JWT Token generated: ${JWT_TOKEN:0:50}..."

echo ""
echo "📋 Test Commands:"
echo "============================================================"

echo ""
echo "🔍 Test 1: Check Subscription Plans (No Auth Required)"
echo "curl -X GET '$PRODUCTION_URL/api/subscriptions/plans' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'x-package-id: $PACKAGE_ID'"

echo ""
echo "🔍 Test 2: Check Subscription Status (Auth Required)"
echo "curl -X GET '$PRODUCTION_URL/api/subscriptions/status' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer $JWT_TOKEN' \\"
echo "  -H 'x-package-id: $PACKAGE_ID'"

echo ""
echo "💳 Test 3: Create Monthly Subscription (The Main Test)"
echo "curl -X POST '$PRODUCTION_URL/api/subscriptions/create-order' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer $JWT_TOKEN' \\"
echo "  -H 'x-package-id: $PACKAGE_ID' \\"
echo "  -d '{"
echo "    \"plan\": \"monthly\","
echo "    \"recurring\": true,"
echo "    \"name\": \"$USER_NAME\","
echo "    \"email\": \"$USER_EMAIL\","
echo "    \"phone\": \"9876543210\""
echo "  }'"

echo ""
echo "💳 Test 4: Create Yearly Subscription"
echo "curl -X POST '$PRODUCTION_URL/api/subscriptions/create-order' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer $JWT_TOKEN' \\"
echo "  -H 'x-package-id: $PACKAGE_ID' \\"
echo "  -d '{"
echo "    \"plan\": \"yearly\","
echo "    \"recurring\": true,"
echo "    \"name\": \"$USER_NAME\","
echo "    \"email\": \"$USER_EMAIL\","
echo "    \"phone\": \"9876543210\""
echo "  }'"

echo ""
echo "🚀 Running Tests..."
echo "============================================================"

echo ""
echo "🔍 Test 1: Subscription Plans"
curl -s -X GET "$PRODUCTION_URL/api/subscriptions/plans" \
  -H 'Content-Type: application/json' \
  -H "x-package-id: $PACKAGE_ID" | jq '.'

echo ""
echo "🔍 Test 2: Subscription Status"
curl -s -X GET "$PRODUCTION_URL/api/subscriptions/status" \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-package-id: $PACKAGE_ID" | jq '.'

echo ""
echo "💳 Test 3: Create Monthly Subscription"
MONTHLY_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X POST "$PRODUCTION_URL/api/subscriptions/create-order" \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-package-id: $PACKAGE_ID" \
  -d "{
    \"plan\": \"monthly\",
    \"recurring\": true,
    \"name\": \"$USER_NAME\",
    \"email\": \"$USER_EMAIL\",
    \"phone\": \"9876543210\"
  }")

# Extract HTTP status and response body
HTTP_STATUS=$(echo "$MONTHLY_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$MONTHLY_RESPONSE" | sed '/HTTP_STATUS:/d')

echo "📡 HTTP Status: $HTTP_STATUS"
echo "📋 Response Body:"
echo "$RESPONSE_BODY" | jq '.'

if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ Monthly subscription creation: SUCCESS"
else
    echo "❌ Monthly subscription creation: FAILED (Status: $HTTP_STATUS)"
    
    # Analyze the error
    if [ "$HTTP_STATUS" = "401" ]; then
        echo "🔍 401 Error: Authentication issue"
        echo "  - Check if JWT token is valid"
        echo "  - Check if user exists in production database"
    elif [ "$HTTP_STATUS" = "500" ]; then
        echo "🔍 500 Error: Server error in subscription creation"
        echo "  - Check production backend logs"
        echo "  - Check payment microservice integration"
        echo "  - Check plan IDs configuration"
    fi
fi

echo ""
echo "💳 Test 4: Create Yearly Subscription"
YEARLY_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X POST "$PRODUCTION_URL/api/subscriptions/create-order" \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-package-id: $PACKAGE_ID" \
  -d "{
    \"plan\": \"yearly\",
    \"recurring\": true,
    \"name\": \"$USER_NAME\",
    \"email\": \"$USER_EMAIL\",
    \"phone\": \"9876543210\"
  }")

# Extract HTTP status and response body
HTTP_STATUS_YEARLY=$(echo "$YEARLY_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_YEARLY=$(echo "$YEARLY_RESPONSE" | sed '/HTTP_STATUS:/d')

echo "📡 HTTP Status: $HTTP_STATUS_YEARLY"
echo "📋 Response Body:"
echo "$RESPONSE_BODY_YEARLY" | jq '.'

if [ "$HTTP_STATUS_YEARLY" = "200" ]; then
    echo "✅ Yearly subscription creation: SUCCESS"
else
    echo "❌ Yearly subscription creation: FAILED (Status: $HTTP_STATUS_YEARLY)"
fi

echo ""
echo "📊 Final Results Summary"
echo "============================================================"
echo "Plans API: ✅ (Should always work)"
echo "Subscription Status: $([ "$?" = "0" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "Monthly Subscription: $([ "$HTTP_STATUS" = "200" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "Yearly Subscription: $([ "$HTTP_STATUS_YEARLY" = "200" ] && echo "✅ PASS" || echo "❌ FAIL")"

echo ""
echo "💡 If tests fail:"
echo "1. Check production backend deployment"
echo "2. Verify payment microservice is running in production"
echo "3. Check production environment variables"
echo "4. Verify plan IDs are correct in production"
echo "5. Check production database connectivity"

# Cleanup
rm -f generate_token.js

echo ""
echo "🎯 cURL testing completed!"
