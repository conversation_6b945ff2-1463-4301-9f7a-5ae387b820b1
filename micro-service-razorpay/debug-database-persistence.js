#!/usr/bin/env node

/**
 * Debug database persistence issue
 * Check if subscriptions are actually being saved with razorpayCustomerId
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

console.log('🔍 Debugging Database Persistence Issue');
console.log('=' .repeat(60));

// Generate JWT for payment microservice
function generatePaymentJWT(userId, packageId) {
  return jwt.sign(
    {
      userId: userId,
      appId: packageId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
}

// Create a subscription and immediately check if it's saved
async function testDatabasePersistence() {
  const testUser = {
    userId: 'db_persist_test_' + Date.now(),
    email: 'db.persist.test.' + Date.now() + '@example.com',
    name: 'DB Persistence Test',
    phone: '9876543210'
  };
  
  console.log('👤 Test User:', testUser.userId);
  console.log('📧 Test Email:', testUser.email);
  
  const token = generatePaymentJWT(testUser.userId, 'com.gumbo.learning');
  
  // Step 1: Create subscription
  console.log('\n📝 Step 1: Creating Subscription');
  
  const createData = {
    userId: testUser.userId,
    planId: 'plan_QkkDaTp9Hje6uC',
    paymentContext: {
      metadata: {
        userName: testUser.name,
        userEmail: testUser.email,
        userPhone: testUser.phone
      },
      subscriptionType: 'premium',
      billingCycle: 'monthly'
    }
  };

  let subscriptionId = null;
  
  try {
    const createResponse = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      createData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    subscriptionId = createResponse.data.data?.subscriptionId;
    console.log('✅ Subscription created successfully');
    console.log('📊 Subscription ID:', subscriptionId);
    console.log('📊 Razorpay Sub ID:', createResponse.data.data?.razorpaySubscriptionId);
    
  } catch (error) {
    console.log('❌ Failed to create subscription:', error.response?.data?.error?.message);
    return { success: false, step: 'creation' };
  }
  
  // Step 2: Immediately check if subscription exists
  console.log('\n📝 Step 2: Checking Subscription Immediately');
  
  try {
    const checkResponse = await axios.get(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscriptions`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 15000
      }
    );

    const subscriptions = checkResponse.data.data?.subscriptions || [];
    console.log(`📊 Found ${subscriptions.length} subscription(s) immediately after creation`);
    
    if (subscriptions.length > 0) {
      subscriptions.forEach((sub, index) => {
        console.log(`   ${index + 1}. ID: ${sub.subscriptionId}`);
        console.log(`      Plan: ${sub.planId}`);
        console.log(`      Status: ${sub.status}`);
        console.log(`      Created: ${sub.createdAt}`);
      });
    }
    
  } catch (error) {
    console.log('❌ Failed to get subscriptions:', error.response?.data?.error?.message);
    return { success: false, step: 'immediate_check' };
  }
  
  // Step 3: Wait and check again
  console.log('\n📝 Step 3: Waiting 3 seconds and checking again');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  try {
    const delayedResponse = await axios.get(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscriptions`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 15000
      }
    );

    const delayedSubscriptions = delayedResponse.data.data?.subscriptions || [];
    console.log(`📊 Found ${delayedSubscriptions.length} subscription(s) after 3 second delay`);
    
    if (delayedSubscriptions.length > 0) {
      delayedSubscriptions.forEach((sub, index) => {
        console.log(`   ${index + 1}. ID: ${sub.subscriptionId}`);
        console.log(`      Plan: ${sub.planId}`);
        console.log(`      Status: ${sub.status}`);
        console.log(`      Created: ${sub.createdAt}`);
      });
    }
    
    return {
      success: true,
      subscriptionCreated: !!subscriptionId,
      immediateCount: subscriptions.length,
      delayedCount: delayedSubscriptions.length,
      persistenceWorking: delayedSubscriptions.length > 0
    };
    
  } catch (error) {
    console.log('❌ Failed to get delayed subscriptions:', error.response?.data?.error?.message);
    return { success: false, step: 'delayed_check' };
  }
}

// Test the customer optimization query specifically
async function testCustomerOptimizationQuery() {
  console.log('\n🔍 Testing Customer Optimization Query Logic');
  
  const testUser = {
    userId: 'cust_opt_query_test_' + Date.now(),
    email: 'cust.opt.query.test.' + Date.now() + '@example.com',
    name: 'Customer Opt Query Test',
    phone: '9876543210'
  };
  
  console.log('👤 Test User:', testUser.userId);
  
  const token = generatePaymentJWT(testUser.userId, 'com.gumbo.learning');
  
  // Create first subscription
  console.log('\n📝 Creating first subscription...');
  
  try {
    const firstResponse = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      {
        userId: testUser.userId,
        planId: 'plan_QkkDaTp9Hje6uC',
        paymentContext: {
          metadata: {
            userName: testUser.name,
            userEmail: testUser.email,
            userPhone: testUser.phone
          }
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('✅ First subscription created');
    console.log('📊 ID:', firstResponse.data.data?.subscriptionId);
    
    // Wait for persistence
    console.log('\n⏳ Waiting 5 seconds for database persistence...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Try second subscription
    console.log('\n📝 Creating second subscription (should reuse customer)...');
    
    const secondResponse = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      {
        userId: testUser.userId,
        planId: 'plan_QkkDw9QRHFT0nG', // Different plan
        paymentContext: {
          metadata: {
            userName: testUser.name,
            userEmail: testUser.email,
            userPhone: testUser.phone
          }
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('✅ Second subscription created successfully!');
    console.log('📊 ID:', secondResponse.data.data?.subscriptionId);
    console.log('🎉 Customer optimization is working!');
    
    return { success: true, optimizationWorking: true };
    
  } catch (error) {
    console.log('❌ Second subscription failed:', error.response?.data?.error?.message);
    console.log('🔧 Customer optimization is not working');
    
    return { 
      success: false, 
      optimizationWorking: false,
      error: error.response?.data?.error?.message 
    };
  }
}

// Main debug function
async function debugDatabasePersistence() {
  console.log('\n🚀 Starting Database Persistence Debug...\n');
  
  // Test 1: Basic persistence
  const persistenceResult = await testDatabasePersistence();
  
  // Test 2: Customer optimization query
  const optimizationResult = await testCustomerOptimizationQuery();
  
  // Analysis
  console.log('\n📊 DATABASE PERSISTENCE DEBUG RESULTS:');
  console.log('=' .repeat(60));
  
  console.log(`📝 Subscription Creation: ${persistenceResult.subscriptionCreated ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`💾 Database Persistence: ${persistenceResult.persistenceWorking ? '✅ WORKING' : '❌ FAILING'}`);
  console.log(`🔄 Customer Optimization: ${optimizationResult.optimizationWorking ? '✅ WORKING' : '❌ FAILING'}`);
  
  if (persistenceResult.persistenceWorking && optimizationResult.optimizationWorking) {
    console.log('\n🎉 ALL SYSTEMS WORKING!');
    console.log('✅ Database persistence is working correctly');
    console.log('✅ Customer optimization is working correctly');
    console.log('✅ The issue may have been resolved by the restart');
    
  } else if (persistenceResult.persistenceWorking && !optimizationResult.optimizationWorking) {
    console.log('\n⚠️ CUSTOMER OPTIMIZATION ISSUE:');
    console.log('✅ Subscriptions are being saved to database');
    console.log('❌ Customer optimization query is not working');
    console.log(`   Error: ${optimizationResult.error}`);
    
    console.log('\n🔧 Possible Issues:');
    console.log('1. razorpayCustomerId not being saved in subscription document');
    console.log('2. Query for existing customer not finding the record');
    console.log('3. Database indexing issue');
    console.log('4. Race condition in customer optimization logic');
    
  } else if (!persistenceResult.persistenceWorking) {
    console.log('\n❌ DATABASE PERSISTENCE ISSUE:');
    console.log('❌ Subscriptions are not being saved to database properly');
    console.log(`   Immediate count: ${persistenceResult.immediateCount}`);
    console.log(`   Delayed count: ${persistenceResult.delayedCount}`);
    
    console.log('\n🔧 Possible Issues:');
    console.log('1. Database connection problem');
    console.log('2. Subscription.save() not working');
    console.log('3. Database transaction not committing');
    console.log('4. MongoDB write concern issue');
  }
  
  console.log('\n📋 Next Steps:');
  if (persistenceResult.persistenceWorking && optimizationResult.optimizationWorking) {
    console.log('1. ✅ Test Android app - should work perfectly now');
    console.log('2. ✅ Monitor production for consistent behavior');
  } else {
    console.log('1. 🔧 Check payment microservice logs for detailed errors');
    console.log('2. 🔧 Verify database connection and write operations');
    console.log('3. 🔧 Test the specific customer optimization query');
  }
  
  return {
    persistenceWorking: persistenceResult.persistenceWorking,
    optimizationWorking: optimizationResult.optimizationWorking
  };
}

// Run the debug
debugDatabasePersistence().catch(console.error);
