#!/usr/bin/env node

/**
 * Simple test to verify database connection and basic operations
 */

const axios = require('axios');

const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';

console.log('🔍 Testing Database Connection and Basic Operations');
console.log('=' .repeat(60));

// Test health endpoint
async function testHealthEndpoint() {
  console.log('\n📝 Testing Health Endpoint');
  
  try {
    const response = await axios.get(`${PAYMENT_MICROSERVICE_URL}/api/health`, {
      timeout: 10000
    });
    
    console.log('✅ Health endpoint working:', response.data);
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ Health endpoint failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Test a simple database operation (if available)
async function testDatabaseOperation() {
  console.log('\n📝 Testing Database Operation via API');
  
  // We'll test by trying to get subscriptions for a non-existent user
  // This should return empty results but confirm database connectivity
  
  const jwt = require('jsonwebtoken');
  const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';
  
  const testUserId = 'db_connection_test_' + Date.now();
  const token = jwt.sign(
    {
      userId: testUserId,
      appId: 'com.gumbo.learning',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
  
  try {
    const response = await axios.get(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscriptions`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 15000
      }
    );
    
    console.log('✅ Database query successful:', {
      subscriptionCount: response.data.data?.subscriptions?.length || 0,
      total: response.data.data?.pagination?.total || 0
    });
    
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ Database query failed:', {
      status: error.response?.status,
      message: error.response?.data?.error?.message || error.message
    });
    
    return { success: false, error: error.response?.data || error.message };
  }
}

// Main test
async function testDatabaseConnection() {
  console.log('\n🚀 Starting Database Connection Test...\n');
  
  // Test 1: Health endpoint
  const healthResult = await testHealthEndpoint();
  
  // Test 2: Database operation
  const dbResult = await testDatabaseOperation();
  
  // Analysis
  console.log('\n📊 DATABASE CONNECTION TEST RESULTS:');
  console.log('=' .repeat(50));
  
  console.log(`🏥 Health Endpoint: ${healthResult.success ? '✅ WORKING' : '❌ FAILING'}`);
  console.log(`💾 Database Query: ${dbResult.success ? '✅ WORKING' : '❌ FAILING'}`);
  
  if (healthResult.success && dbResult.success) {
    console.log('\n🎉 DATABASE CONNECTION WORKING!');
    console.log('✅ Payment microservice is healthy');
    console.log('✅ Database queries are working');
    console.log('✅ The issue is likely in the subscription save logic');
    
    console.log('\n📋 Next Steps:');
    console.log('1. ✅ Deploy the enhanced database persistence fix');
    console.log('2. ✅ Check payment microservice logs for detailed errors');
    console.log('3. ✅ Test subscription creation with enhanced error handling');
    
  } else if (healthResult.success && !dbResult.success) {
    console.log('\n⚠️ DATABASE QUERY ISSUE:');
    console.log('✅ Payment microservice is healthy');
    console.log('❌ Database queries are failing');
    
    console.log('\n🔧 Possible Issues:');
    console.log('1. Database connection not established');
    console.log('2. Authentication/authorization issue');
    console.log('3. Database server down or unreachable');
    console.log('4. MongoDB connection string incorrect');
    
  } else {
    console.log('\n❌ SERVICE UNAVAILABLE:');
    console.log('❌ Payment microservice is not responding');
    console.log('🔧 Check if the service is running and accessible');
  }
  
  return {
    healthWorking: healthResult.success,
    databaseWorking: dbResult.success
  };
}

// Run the test
testDatabaseConnection().catch(console.error);
