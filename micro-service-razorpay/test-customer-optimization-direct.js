#!/usr/bin/env node

/**
 * Test customer optimization directly with payment microservice
 * This isolates the issue from seekho-backend authentication
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

console.log('🧪 Testing Customer Optimization - Direct Payment Microservice');
console.log('=' .repeat(70));
console.log('🎯 First subscription creates customer, second should reuse');

// Generate JWT for payment microservice
function generatePaymentJWT(userId, packageId) {
  return jwt.sign(
    {
      userId: userId,
      appId: packageId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
}

// Test subscription creation
async function createSubscription(userData, planId, attemptNumber) {
  console.log(`\n📝 Attempt ${attemptNumber}: Creating Subscription`);
  console.log(`   User: ${userData.userId}`);
  console.log(`   Email: ${userData.email}`);
  console.log(`   Plan: ${planId}`);
  
  const token = generatePaymentJWT(userData.userId, 'com.gumbo.learning');
  
  const requestData = {
    userId: userData.userId,
    planId: planId,
    paymentContext: {
      metadata: {
        userName: userData.name,
        userEmail: userData.email,
        userPhone: userData.phone
      },
      subscriptionType: 'premium',
      billingCycle: planId.includes('monthly') ? 'monthly' : 'yearly'
    }
  };

  console.log('🔗 Endpoint:', `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`);

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ SUCCESS (${responseTime}ms):`);
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('📊 Razorpay Sub ID:', response.data.data?.razorpaySubscriptionId);
    console.log('📊 Status:', response.data.data?.status);
    
    return {
      success: true,
      responseTime,
      subscriptionId: response.data.data?.subscriptionId,
      razorpaySubscriptionId: response.data.data?.razorpaySubscriptionId,
      data: response.data
    };

  } catch (error) {
    console.log(`❌ FAILED:`);
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error Code:', error.response?.data?.error?.code);
    console.log('📊 Error Message:', error.response?.data?.error?.message);
    console.log('📊 Full Response:', JSON.stringify(error.response?.data, null, 2));
    
    return {
      success: false,
      error: error.response?.data,
      status: error.response?.status,
      errorMessage: error.response?.data?.error?.message || 'Unknown error'
    };
  }
}

// Check user's existing subscriptions
async function checkExistingSubscriptions(userData) {
  console.log('\n📝 Checking Existing Subscriptions');
  console.log(`   User: ${userData.userId}`);
  
  const token = generatePaymentJWT(userData.userId, 'com.gumbo.learning');
  
  try {
    const response = await axios.get(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscriptions`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 15000
      }
    );

    const subscriptions = response.data.data?.subscriptions || [];
    
    console.log(`📊 Found ${subscriptions.length} existing subscription(s)`);
    
    subscriptions.forEach((sub, index) => {
      console.log(`   ${index + 1}. ID: ${sub.subscriptionId}`);
      console.log(`      Razorpay Customer ID: ${sub.razorpayCustomerId || 'N/A'}`);
      console.log(`      Plan: ${sub.planId}`);
      console.log(`      Status: ${sub.status}`);
    });
    
    return subscriptions;

  } catch (error) {
    console.log('❌ Failed to get subscriptions:', error.response?.data?.error?.message);
    return [];
  }
}

// Main test for customer optimization
async function testCustomerOptimization() {
  console.log('\n🚀 Starting Customer Optimization Test...\n');
  
  // Create a unique test user
  const testUser = {
    userId: 'customer_opt_test_' + Date.now(),
    name: 'Customer Optimization Test',
    email: 'customer.opt.test.' + Date.now() + '@example.com',
    phone: '9876543210'
  };
  
  console.log('👤 Test User:');
  console.log(`   User ID: ${testUser.userId}`);
  console.log(`   Email: ${testUser.email}`);
  
  // Test 1: First subscription (should create customer)
  console.log('\n🔸 FIRST SUBSCRIPTION (Should create customer):');
  const firstResult = await createSubscription(testUser, 'plan_QkkDaTp9Hje6uC', 1);
  
  if (!firstResult.success) {
    console.log('❌ First subscription failed - cannot test optimization');
    return { success: false, reason: 'First subscription failed' };
  }
  
  // Check subscriptions after first creation
  console.log('\n🔍 Checking subscriptions after first creation:');
  const subscriptionsAfterFirst = await checkExistingSubscriptions(testUser);
  
  // Wait for database persistence
  console.log('\n⏳ Waiting 5 seconds for database persistence...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Test 2: Second subscription (should reuse customer)
  console.log('\n🔸 SECOND SUBSCRIPTION (Should reuse customer):');
  const secondResult = await createSubscription(testUser, 'plan_QkkDw9QRHFT0nG', 2);
  
  // Check subscriptions after second creation
  if (secondResult.success) {
    console.log('\n🔍 Checking subscriptions after second creation:');
    const subscriptionsAfterSecond = await checkExistingSubscriptions(testUser);
  }
  
  // Analysis
  console.log('\n📊 CUSTOMER OPTIMIZATION ANALYSIS:');
  console.log('=' .repeat(60));
  
  const firstTime = firstResult.responseTime;
  const secondTime = secondResult.success ? secondResult.responseTime : 0;
  const timeDifference = firstTime - secondTime;
  const isOptimized = secondResult.success && secondTime < firstTime && timeDifference > 500;
  
  console.log(`🥇 First Subscription: ${firstResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`🥈 Second Subscription: ${secondResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (firstResult.success && secondResult.success) {
    console.log('\n🎉 CUSTOMER OPTIMIZATION WORKING!');
    console.log('✅ Both subscriptions created successfully');
    
    console.log('\n📊 Performance Analysis:');
    console.log(`   First subscription: ${firstTime}ms (customer creation)`);
    console.log(`   Second subscription: ${secondTime}ms (customer reuse)`);
    console.log(`   Time difference: ${timeDifference}ms`);
    console.log(`   Optimization detected: ${isOptimized ? 'YES ✅' : 'NO ❌'}`);
    
    if (isOptimized) {
      console.log('🚀 Customer ID reuse is working - significant performance improvement!');
    } else {
      console.log('⚠️ Customer ID reuse may not be working - similar response times');
    }
    
  } else if (firstResult.success && !secondResult.success) {
    console.log('\n❌ CUSTOMER OPTIMIZATION ISSUE CONFIRMED');
    console.log('✅ First subscription works (customer creation)');
    console.log('❌ Second subscription fails (customer reuse)');
    
    console.log('\n🔍 Issue Details:');
    console.log(`   First subscription time: ${firstTime}ms`);
    console.log(`   Second subscription error: ${secondResult.errorMessage}`);
    console.log(`   Subscriptions after first: ${subscriptionsAfterFirst.length}`);
    
    console.log('\n🔧 Likely Root Causes:');
    console.log('1. Customer ID not being persisted in subscription document');
    console.log('2. getOrCreateCustomerId not finding existing customer');
    console.log('3. Database query for existing customer failing');
    console.log('4. Race condition in customer optimization logic');
    
    console.log('\n💡 Debugging Steps:');
    console.log('1. Check payment microservice logs for customer optimization queries');
    console.log('2. Verify subscription documents have razorpayCustomerId field');
    console.log('3. Test the findOne query for existing subscriptions');
    console.log('4. Check if getOrCreateCustomerId is being called correctly');
    
  } else {
    console.log('\n❌ BROADER PAYMENT SYSTEM ISSUE');
    console.log('❌ First subscription failed - issue is not customer optimization');
    console.log(`   Error: ${firstResult.errorMessage}`);
  }
  
  console.log('\n📋 Next Steps:');
  if (firstResult.success && secondResult.success) {
    console.log('1. ✅ Test with Android app - should work perfectly');
    console.log('2. ✅ Monitor production for performance improvements');
  } else if (firstResult.success && !secondResult.success) {
    console.log('1. 🔧 Check payment microservice logs for customer optimization');
    console.log('2. 🔧 Verify database persistence of razorpayCustomerId');
    console.log('3. 🔧 Test the customer search query directly');
  } else {
    console.log('1. 🔧 Fix the basic subscription creation issue first');
    console.log('2. 🔧 Check payment microservice deployment');
  }
  
  return {
    firstSuccess: firstResult.success,
    secondSuccess: secondResult.success,
    optimizationWorking: isOptimized,
    issueType: firstResult.success ? (secondResult.success ? 'NONE' : 'CUSTOMER_OPTIMIZATION') : 'BASIC_SUBSCRIPTION'
  };
}

// Run the test
testCustomerOptimization().catch(console.error);
