#!/usr/bin/env node

/**
 * Test with the exact real user data from Android app logs
 * This will verify if the deployment fixed the original issue
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

// Configuration
const SEEKHO_BACKEND_URL = 'https://learner.netaapp.in';
const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

// Real user data from Android app logs
const REAL_ANDROID_USER = {
  name: 'praveen',
  email: '<EMAIL>',
  phone: '9403012499',
  packageId: 'com.gumbo.learning'
};

console.log('🧪 Testing Real Android User After Deployment');
console.log('=' .repeat(60));
console.log('📱 Real user data from Android app logs:');
console.log(`   Name: ${REAL_ANDROID_USER.name}`);
console.log(`   Email: ${REAL_ANDROID_USER.email}`);
console.log(`   Phone: ${REAL_ANDROID_USER.phone}`);
console.log(`   Package: ${REAL_ANDROID_USER.packageId}`);

// Generate JWT for seekho-backend (simulating real user)
function generateSeekhoJWT() {
  return jwt.sign(
    {
      id: '686260bdd5621fe373be8d39', // Real user ID from production logs
      email: REAL_ANDROID_USER.email,
      name: REAL_ANDROID_USER.name,
      phone: REAL_ANDROID_USER.phone
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
}

// Generate JWT for payment microservice
function generatePaymentJWT(userId, packageId) {
  return jwt.sign(
    {
      userId: userId,
      appId: packageId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
}

// Test 1: Exact Android App Flow
async function testExactAndroidFlow() {
  console.log('\n📝 Test 1: Exact Android App Flow');
  console.log('🔗 Endpoint: https://learner.netaapp.in/api/subscriptions/create-order');
  
  const token = generateSeekhoJWT();
  
  // Exact request from Android app logs
  const requestData = {
    plan: 'monthly',
    recurring: true,
    name: REAL_ANDROID_USER.name,
    email: REAL_ANDROID_USER.email,
    phone: REAL_ANDROID_USER.phone
  };

  console.log('📋 Request body:', JSON.stringify(requestData, null, 2));

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': REAL_ANDROID_USER.packageId
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ SUCCESS (${responseTime}ms):`);
    console.log('📊 Response status:', response.status);
    console.log('📊 Response body:', JSON.stringify(response.data, null, 2));
    
    return {
      success: true,
      responseTime,
      data: response.data,
      status: response.status
    };

  } catch (error) {
    console.log('❌ FAILED:');
    console.log('📊 Response status:', error.response?.status);
    console.log('📊 Response body:', JSON.stringify(error.response?.data, null, 2));
    
    // Check if this is the same error as before
    const errorMessage = error.response?.data?.message || '';
    const isSameError = errorMessage.includes('Customer already exists or customer creation failed');
    const hasImprovedError = errorMessage.length > 50 || errorMessage.includes('try again');
    
    console.log(`🔍 Same error as before: ${isSameError ? 'YES' : 'NO'}`);
    console.log(`🔍 Improved error message: ${hasImprovedError ? 'YES' : 'NO'}`);
    
    return {
      success: false,
      error: error.response?.data,
      status: error.response?.status,
      isSameError,
      hasImprovedError
    };
  }
}

// Test 2: Direct Payment Microservice with Real User
async function testPaymentMicroserviceWithRealUser() {
  console.log('\n📝 Test 2: Direct Payment Microservice with Real User');
  console.log('🔗 Endpoint: https://payments.netaapp.in/api/payment/subscription');
  
  const userId = '686260bdd5621fe373be8d39'; // Real user ID
  const token = generatePaymentJWT(userId, REAL_ANDROID_USER.packageId);
  
  const requestData = {
    userId: userId,
    planId: 'plan_QkkDaTp9Hje6uC', // Monthly plan
    paymentContext: {
      metadata: {
        userName: REAL_ANDROID_USER.name,
        userEmail: REAL_ANDROID_USER.email,
        userPhone: REAL_ANDROID_USER.phone
      },
      subscriptionType: 'premium',
      billingCycle: 'monthly'
    }
  };

  console.log('📋 Request body:', JSON.stringify(requestData, null, 2));

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': REAL_ANDROID_USER.packageId
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ SUCCESS (${responseTime}ms):`);
    console.log('📊 Response:', JSON.stringify(response.data, null, 2));
    
    return {
      success: true,
      responseTime,
      data: response.data
    };

  } catch (error) {
    console.log('❌ FAILED:');
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error Code:', error.response?.data?.error?.code);
    console.log('📊 Error Message:', error.response?.data?.error?.message);
    console.log('📊 Full Response:', JSON.stringify(error.response?.data, null, 2));
    
    // Check if error handling is improved
    const errorMessage = error.response?.data?.error?.message || '';
    const hasDetailedError = errorMessage.length > 30;
    const hasHelpfulMessage = errorMessage.includes('try again') || errorMessage.includes('contact support');
    
    console.log(`🔍 Detailed error: ${hasDetailedError ? 'YES' : 'NO'}`);
    console.log(`🔍 Helpful message: ${hasHelpfulMessage ? 'YES' : 'NO'}`);
    
    return {
      success: false,
      error: error.response?.data,
      hasDetailedError,
      hasHelpfulMessage
    };
  }
}

// Test 3: Test with a fresh email to see if new users work
async function testWithFreshEmail() {
  console.log('\n📝 Test 3: Test with Fresh Email (New User)');
  
  const freshEmail = 'fresh.test.' + Date.now() + '@example.com';
  console.log(`📧 Fresh email: ${freshEmail}`);
  
  const token = generateSeekhoJWT();
  
  const requestData = {
    plan: 'monthly',
    recurring: true,
    name: 'Fresh Test User',
    email: freshEmail,
    phone: '9876543210'
  };

  try {
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': REAL_ANDROID_USER.packageId
        },
        timeout: 30000
      }
    );

    console.log('✅ Fresh email SUCCESS:');
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('📊 Status:', response.data.data?.status);
    
    return { success: true, data: response.data };

  } catch (error) {
    console.log('❌ Fresh email FAILED:');
    console.log('📊 Error:', error.response?.data?.message);
    
    return { success: false, error: error.response?.data };
  }
}

// Main test execution
async function runRealUserTests() {
  console.log('\n🚀 Starting Real Android User Tests...\n');
  
  // Test 1: Exact Android flow
  const androidResult = await testExactAndroidFlow();
  
  // Test 2: Direct payment microservice
  const paymentResult = await testPaymentMicroserviceWithRealUser();
  
  // Test 3: Fresh email test
  const freshResult = await testWithFreshEmail();
  
  // Analysis
  console.log('\n📊 DEPLOYMENT TEST RESULTS:');
  console.log('=' .repeat(50));
  
  console.log(`📱 Android App Flow: ${androidResult.success ? '✅ WORKING' : '❌ FAILING'}`);
  console.log(`💳 Payment Microservice: ${paymentResult.success ? '✅ WORKING' : '❌ FAILING'}`);
  console.log(`🆕 Fresh Email Test: ${freshResult.success ? '✅ WORKING' : '❌ FAILING'}`);
  
  if (androidResult.success) {
    console.log('\n🎉 DEPLOYMENT SUCCESSFUL!');
    console.log('✅ Real Android user can now create subscriptions');
    console.log('✅ Customer creation fix is working in production');
    console.log('✅ End-to-end payment flow is functional');
    
    console.log('\n📋 Next Steps:');
    console.log('1. ✅ Test payment completion in Android app');
    console.log('2. ✅ Verify Razorpay payment processing');
    console.log('3. ✅ Monitor production metrics');
    
  } else {
    console.log('\n⚠️ DEPLOYMENT ISSUES DETECTED:');
    
    if (androidResult.isSameError) {
      console.log('❌ Still getting the same error as before deployment');
      console.log('🔧 The fix may not be fully deployed or there\'s a caching issue');
    }
    
    if (!androidResult.hasImprovedError) {
      console.log('❌ Error messages haven\'t improved');
      console.log('🔧 Enhanced error handling may not be active');
    }
    
    if (paymentResult.success && !androidResult.success) {
      console.log('⚠️ Payment microservice works but seekho-backend integration fails');
      console.log('🔧 Issue might be in seekho-backend → payment microservice communication');
    }
    
    if (freshResult.success && !androidResult.success) {
      console.log('⚠️ Fresh emails work but existing user fails');
      console.log('🔧 Issue is specific to the real user email: ' + REAL_ANDROID_USER.email);
    }
    
    console.log('\n📋 Debugging Steps:');
    console.log('1. 🔧 Check payment microservice logs for detailed errors');
    console.log('2. 🔧 Verify deployment completed successfully');
    console.log('3. 🔧 Check if there are any caching issues');
    console.log('4. 🔧 Test with Razorpay dashboard for customer conflicts');
  }
  
  return {
    androidFlow: androidResult.success,
    paymentMicroservice: paymentResult.success,
    freshEmail: freshResult.success,
    overallSuccess: androidResult.success && paymentResult.success
  };
}

// Run the tests
runRealUserTests().catch(console.error);
