#!/usr/bin/env node

/**
 * Simple test to verify if the enhanced deployment is active
 * This will trigger the enhanced logging we added
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

console.log('🔍 Verifying Enhanced Deployment');
console.log('=' .repeat(50));

async function verifyEnhancedDeployment() {
  const testUserId = 'deployment_verify_' + Date.now();
  const testEmail = 'deployment.verify.' + Date.now() + '@example.com';
  
  console.log('👤 Test User:', testUserId);
  console.log('📧 Test Email:', testEmail);
  
  const token = jwt.sign(
    {
      userId: testUserId,
      appId: 'com.gumbo.learning',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
  
  const requestData = {
    userId: testUserId,
    planId: 'plan_QkkDaTp9Hje6uC',
    paymentContext: {
      metadata: {
        userName: 'Deployment Verify Test',
        userEmail: testEmail,
        userPhone: '9876543210'
      },
      subscriptionType: 'premium',
      billingCycle: 'monthly'
    }
  };

  console.log('\n📝 Testing subscription creation...');

  try {
    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('✅ SUCCESS - Enhanced deployment is working!');
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('🎉 The enhanced database persistence fix is active!');
    
    return { success: true, deploymentActive: true };

  } catch (error) {
    const errorMessage = error.response?.data?.error?.message || '';
    
    console.log('❌ Request failed with error:', errorMessage);
    
    // Check if we're getting enhanced error messages
    const hasEnhancedError = errorMessage.includes('Database') || 
                            errorMessage.includes('connection') ||
                            errorMessage.includes('verification') ||
                            errorMessage.includes('CRITICAL') ||
                            errorMessage.length > 50;
    
    if (hasEnhancedError) {
      console.log('✅ ENHANCED DEPLOYMENT IS ACTIVE!');
      console.log('📋 Enhanced error handling is working');
      console.log('🔍 Check PM2 logs for detailed error information');
      return { success: false, deploymentActive: true, enhancedError: errorMessage };
    } else {
      console.log('❌ ENHANCED DEPLOYMENT NOT ACTIVE');
      console.log('🔧 Still getting generic error message');
      console.log('📋 Need to redeploy the enhanced fix');
      return { success: false, deploymentActive: false, genericError: errorMessage };
    }
  }
}

// Run verification
verifyEnhancedDeployment()
  .then(result => {
    console.log('\n📊 DEPLOYMENT VERIFICATION RESULT:');
    console.log('=' .repeat(40));
    
    if (result.success) {
      console.log('🎉 DEPLOYMENT SUCCESSFUL!');
      console.log('✅ Enhanced database persistence is working');
      console.log('✅ Customer optimization should work');
      console.log('✅ Android app should work perfectly');
      
    } else if (result.deploymentActive) {
      console.log('⚠️ DEPLOYMENT ACTIVE BUT ISSUES REMAIN:');
      console.log('✅ Enhanced error handling is working');
      console.log('❌ Database persistence still has issues');
      console.log('📋 Check PM2 logs for detailed error information:');
      console.log('   pm2 logs payment-service --lines 50');
      
    } else {
      console.log('❌ DEPLOYMENT NOT ACTIVE:');
      console.log('🔧 Enhanced code is not running');
      console.log('📋 Need to redeploy properly');
      
      console.log('\n🔧 Deployment Steps:');
      console.log('1. pm2 stop payment-service');
      console.log('2. pm2 delete payment-service');
      console.log('3. git pull origin main');
      console.log('4. npm run build');
      console.log('5. pm2 start ecosystem.config.js');
    }
  })
  .catch(console.error);
