#!/usr/bin/env node

/**
 * Test the fix after implementing local storage in seekho-backend
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const SEEKHO_BACKEND_URL = 'https://learner.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

console.log('🧪 Testing After Local Storage Fix');
console.log('=' .repeat(60));
console.log('🎯 Testing if local subscription storage fixes customer optimization');

// Generate JWT for seekho-backend
function generateSeekhoJWT(userId, userData) {
  return jwt.sign(
    {
      id: userId,
      email: userData.email,
      name: userData.name,
      phone: userData.phone
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
}

// Test the complete flow via seekho-backend
async function testCompleteFlow() {
  const testUser = {
    userId: 'local_storage_test_' + Date.now(),
    name: 'Local Storage Test User',
    email: 'local.storage.test.' + Date.now() + '@example.com',
    phone: '9876543210'
  };
  
  console.log('\n👤 Test User:');
  console.log(`   User ID: ${testUser.userId}`);
  console.log(`   Email: ${testUser.email}`);
  console.log(`   Name: ${testUser.name}`);
  
  const token = generateSeekhoJWT(testUser.userId, testUser);
  
  // Test 1: First subscription (should create local record)
  console.log('\n🔸 FIRST SUBSCRIPTION (Should create local record):');
  
  const requestData = {
    plan: 'monthly',
    recurring: true,
    name: testUser.name,
    email: testUser.email,
    phone: testUser.phone
  };

  console.log('🔗 Endpoint:', `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`);
  console.log('📋 Request:', JSON.stringify(requestData, null, 2));

  let firstResult;
  try {
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('✅ First subscription SUCCESS:');
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('📊 Razorpay Sub ID:', response.data.data?.razorpaySubscriptionId);
    console.log('📊 Status:', response.data.data?.status);
    
    firstResult = { success: true, data: response.data };

  } catch (error) {
    console.log('❌ First subscription FAILED:');
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error:', error.response?.data?.message);
    
    firstResult = { success: false, error: error.response?.data };
  }
  
  if (!firstResult.success) {
    console.log('\n❌ Cannot test second subscription - first failed');
    return { firstSuccess: false, secondSuccess: false };
  }
  
  // Wait for local storage to complete
  console.log('\n⏳ Waiting 5 seconds for local storage to complete...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Test 2: Second subscription (should use customer optimization)
  console.log('\n🔸 SECOND SUBSCRIPTION (Should use customer optimization):');
  
  const secondRequestData = {
    plan: 'yearly',
    recurring: true,
    name: testUser.name,
    email: testUser.email,
    phone: testUser.phone
  };

  let secondResult;
  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      secondRequestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ Second subscription SUCCESS (${responseTime}ms):`);
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('📊 Razorpay Sub ID:', response.data.data?.razorpaySubscriptionId);
    
    secondResult = { success: true, responseTime, data: response.data };

  } catch (error) {
    console.log('❌ Second subscription FAILED:');
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error:', error.response?.data?.message);
    
    secondResult = { success: false, error: error.response?.data };
  }
  
  return {
    firstSuccess: firstResult.success,
    secondSuccess: secondResult.success,
    secondResponseTime: secondResult.responseTime
  };
}

// Test with Praveen's data specifically
async function testPraveenData() {
  console.log('\n📝 Testing with Praveen\'s Data:');
  
  const praveenData = {
    userId: 'praveen_test_after_fix_' + Date.now(),
    name: 'Praveen kumar Singh',
    email: '<EMAIL>',
    phone: '9403012499'
  };
  
  console.log(`👤 Testing with: ${praveenData.email}`);
  
  const token = generateSeekhoJWT(praveenData.userId, praveenData);
  
  const requestData = {
    plan: 'monthly',
    recurring: true,
    name: praveenData.name,
    email: praveenData.email,
    phone: praveenData.phone
  };

  try {
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('✅ Praveen\'s subscription SUCCESS:');
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('🎉 The customer duplication issue is resolved!');
    
    return { success: true };

  } catch (error) {
    console.log('❌ Praveen\'s subscription still FAILED:');
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error:', error.response?.data?.message);
    
    // Check if it's still the same customer duplication error
    const errorMessage = error.response?.data?.message || '';
    const isCustomerError = errorMessage.includes('customer') || 
                           errorMessage.includes('already exists') ||
                           errorMessage.includes('Failed to create subscription');
    
    if (isCustomerError) {
      console.log('🔧 Still a customer duplication issue - need additional fix');
    } else {
      console.log('🔧 Different error - progress made!');
    }
    
    return { success: false, isCustomerError };
  }
}

// Main test execution
async function runLocalStorageTest() {
  console.log('\n🚀 Starting Local Storage Fix Test...\n');
  
  // Test 1: Complete flow with new user
  const flowResult = await testCompleteFlow();
  
  // Test 2: Praveen's specific data
  const praveenResult = await testPraveenData();
  
  // Analysis
  console.log('\n📊 LOCAL STORAGE FIX TEST RESULTS:');
  console.log('=' .repeat(60));
  
  console.log(`🔸 New User Flow:`);
  console.log(`   First Subscription: ${flowResult.firstSuccess ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`   Second Subscription: ${flowResult.secondSuccess ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (flowResult.secondResponseTime) {
    console.log(`   Second Response Time: ${flowResult.secondResponseTime}ms`);
  }
  
  console.log(`🔸 Praveen's Data: ${praveenResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (flowResult.firstSuccess && flowResult.secondSuccess) {
    console.log('\n🎉 LOCAL STORAGE FIX IS WORKING!');
    console.log('✅ Customer optimization is now working');
    console.log('✅ Local subscription records enable customer reuse');
    console.log('✅ Future subscriptions will work correctly');
    
    if (praveenResult.success) {
      console.log('✅ Praveen\'s issue is also resolved!');
    } else {
      console.log('⚠️ Praveen\'s issue needs additional handling for existing Razorpay customer');
    }
    
  } else if (flowResult.firstSuccess && !flowResult.secondSuccess) {
    console.log('\n⚠️ PARTIAL FIX:');
    console.log('✅ First subscriptions work');
    console.log('❌ Customer optimization still not working');
    console.log('🔧 May need additional database query optimization');
    
  } else {
    console.log('\n❌ LOCAL STORAGE FIX NOT WORKING:');
    console.log('❌ Basic subscription creation still failing');
    console.log('🔧 Need to check deployment and error logs');
  }
  
  console.log('\n📋 Next Steps:');
  if (flowResult.firstSuccess && flowResult.secondSuccess && praveenResult.success) {
    console.log('1. ✅ Test Android app - should work perfectly');
    console.log('2. ✅ Monitor production for consistent behavior');
  } else if (flowResult.firstSuccess && flowResult.secondSuccess) {
    console.log('1. ✅ New users will work correctly');
    console.log('2. 🔧 Handle existing Razorpay customers (like Praveen)');
    console.log('3. 🔧 Consider customer migration or cleanup');
  } else {
    console.log('1. 🔧 Check deployment of both services');
    console.log('2. 🔧 Verify local storage is working in seekho-backend');
    console.log('3. 🔧 Check payment microservice logs');
  }
  
  return {
    localStorageWorking: flowResult.firstSuccess && flowResult.secondSuccess,
    praveenFixed: praveenResult.success
  };
}

// Run the test
runLocalStorageTest().catch(console.error);
