#!/usr/bin/env node

/**
 * Test the exact validation issue with the real request format
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

console.log('🔍 Testing Validation Issue');
console.log('=' .repeat(50));

// Test with the exact request format from seekho-backend
async function testValidationIssue() {
  const userId = 'validation_test_' + Date.now();
  const token = jwt.sign(
    {
      userId: userId,
      appId: 'com.gumbo.learning',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
  
  // Exact request format from seekho-backend
  const requestData = {
    userId: userId,
    planId: 'plan_QkkDaTp9Hje6uC',
    paymentContext: {
      subscriptionType: 'premium',
      billingCycle: 'monthly',
      recurring: true,
      metadata: {
        userName: '<PERSON><PERSON><PERSON> kuma<PERSON>',
        userEmail: '<EMAIL>',
        userPhone: '9403012499',  // Exactly 10 digits, starts with 9
        userId: userId,
        packageId: 'com.gumbo.learning'
      }
    }
  };

  console.log('📋 Testing with exact request format:');
  console.log(JSON.stringify(requestData, null, 2));
  
  console.log('\n🔍 Validation Analysis:');
  console.log(`Phone: "${requestData.paymentContext.metadata.userPhone}"`);
  console.log(`Length: ${requestData.paymentContext.metadata.userPhone.length}`);
  console.log(`Starts with 6-9: ${/^[6-9]/.test(requestData.paymentContext.metadata.userPhone)}`);
  console.log(`All digits: ${/^\d+$/.test(requestData.paymentContext.metadata.userPhone)}`);
  console.log(`Regex match: ${/^[6-9]\d{9}$/.test(requestData.paymentContext.metadata.userPhone)}`);

  try {
    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('\n✅ SUCCESS - Validation passed!');
    console.log('📊 Response:', response.data);
    
    return { success: true };

  } catch (error) {
    console.log('\n❌ VALIDATION FAILED:');
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error:', JSON.stringify(error.response?.data, null, 2));
    
    // Check if it's a validation error
    if (error.response?.data?.error?.code === 'VALIDATION_ERROR') {
      console.log('\n🔍 Validation Error Details:');
      const details = error.response.data.error.details || [];
      details.forEach((detail, index) => {
        console.log(`   ${index + 1}. Field: ${detail.field}`);
        console.log(`      Message: ${detail.message}`);
        console.log(`      Code: ${detail.code}`);
      });
      
      // Check if phone validation is the issue
      const phoneError = details.find(d => d.field.includes('userPhone'));
      if (phoneError) {
        console.log('\n🎯 PHONE VALIDATION ISSUE CONFIRMED:');
        console.log(`   Field: ${phoneError.field}`);
        console.log(`   Message: ${phoneError.message}`);
        console.log(`   Expected: 10 digits starting with 6-9`);
        console.log(`   Actual: "${requestData.paymentContext.metadata.userPhone}"`);
      }
    }
    
    return { success: false, error: error.response?.data };
  }
}

// Test with different phone number formats
async function testPhoneFormats() {
  console.log('\n📝 Testing Different Phone Formats:');
  
  const phoneTests = [
    { phone: '9403012499', description: 'Original (10 digits, starts with 9)' },
    { phone: '8403012499', description: '10 digits, starts with 8' },
    { phone: '7403012499', description: '10 digits, starts with 7' },
    { phone: '6403012499', description: '10 digits, starts with 6' },
    { phone: '5403012499', description: '10 digits, starts with 5 (should fail)' },
    { phone: '94030124999', description: '11 digits (should fail)' },
    { phone: '940301249', description: '9 digits (should fail)' }
  ];
  
  for (const test of phoneTests) {
    console.log(`\n🔸 Testing: ${test.description}`);
    console.log(`   Phone: "${test.phone}"`);
    
    const userId = 'phone_test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
    const token = jwt.sign(
      {
        userId: userId,
        appId: 'com.gumbo.learning',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
      },
      JWT_SECRET
    );
    
    const requestData = {
      userId: userId,
      planId: 'plan_QkkDaTp9Hje6uC',
      paymentContext: {
        subscriptionType: 'premium',
        billingCycle: 'monthly',
        recurring: true,
        metadata: {
          userName: 'Phone Test User',
          userEmail: '<EMAIL>',
          userPhone: test.phone,
          userId: userId,
          packageId: 'com.gumbo.learning'
        }
      }
    };

    try {
      const response = await axios.post(
        `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'x-app-id': 'com.gumbo.learning'
          },
          timeout: 15000
        }
      );

      console.log('   ✅ PASSED validation');

    } catch (error) {
      if (error.response?.data?.error?.code === 'VALIDATION_ERROR') {
        const phoneError = error.response.data.error.details?.find(d => d.field.includes('userPhone'));
        if (phoneError) {
          console.log(`   ❌ FAILED: ${phoneError.message}`);
        } else {
          console.log('   ❌ FAILED: Other validation error');
        }
      } else {
        console.log('   ❌ FAILED: Non-validation error');
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// Main test execution
async function runValidationTests() {
  console.log('\n🚀 Starting Validation Tests...\n');
  
  // Test 1: Exact validation issue
  const validationResult = await testValidationIssue();
  
  // Test 2: Different phone formats
  await testPhoneFormats();
  
  // Analysis
  console.log('\n📊 VALIDATION TEST RESULTS:');
  console.log('=' .repeat(50));
  
  if (validationResult.success) {
    console.log('🎉 VALIDATION ISSUE RESOLVED!');
    console.log('✅ The exact request format from seekho-backend now works');
    console.log('✅ Phone number validation is working correctly');
    
    console.log('\n📋 Next Steps:');
    console.log('1. ✅ Test Android app - should work now');
    console.log('2. ✅ Verify end-to-end payment flow');
    
  } else {
    console.log('⚠️ VALIDATION ISSUE PERSISTS:');
    console.log('❌ The request format is still failing validation');
    
    const error = validationResult.error;
    if (error?.error?.code === 'VALIDATION_ERROR') {
      console.log('🔧 This is a validation schema issue in the payment microservice');
      console.log('🔧 The validation schema needs to be adjusted');
    } else {
      console.log('🔧 This might be a different type of error');
    }
    
    console.log('\n🔧 Debugging Steps:');
    console.log('1. Check the validation schema in validation.ts');
    console.log('2. Verify the request format matches the schema');
    console.log('3. Check if there are additional validation rules');
  }
  
  return validationResult;
}

// Run the tests
runValidationTests().catch(console.error);
