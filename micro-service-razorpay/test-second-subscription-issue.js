#!/usr/bin/env node

/**
 * Test the specific issue: New user works first time, fails second time
 * This tests the customer optimization logic
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

// Configuration
const SEEKHO_BACKEND_URL = 'https://learner.netaapp.in';
const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

console.log('🧪 Testing Second Subscription Issue');
console.log('=' .repeat(60));
console.log('🎯 New user works first time, fails second time');

// Generate JWT for seekho-backend
function generateSeekhoJWT(userId, userData) {
  return jwt.sign(
    {
      id: userId,
      email: userData.email,
      name: userData.name,
      phone: userData.phone
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
}

// Generate JWT for payment microservice
function generatePaymentJWT(userId, packageId) {
  return jwt.sign(
    {
      userId: userId,
      appId: packageId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
}

// Test via Seekho-Backend (Flutter → Seekho-Backend → Payment Microservice)
async function testViaSeekhoBackend(userData, planType, attemptNumber) {
  console.log(`\n📝 Attempt ${attemptNumber}: Testing via Seekho-Backend`);
  console.log(`   Plan: ${planType}`);
  console.log(`   Email: ${userData.email}`);
  
  const token = generateSeekhoJWT(userData.userId, userData);
  
  const requestData = {
    plan: planType,
    recurring: true,
    name: userData.name,
    email: userData.email,
    phone: userData.phone
  };

  console.log('🔗 Endpoint:', `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`);
  console.log('📋 Request:', JSON.stringify(requestData, null, 2));

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ SUCCESS (${responseTime}ms):`);
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('📊 Status:', response.data.data?.status);
    console.log('📊 Short URL:', response.data.data?.shortUrl);
    
    return {
      success: true,
      responseTime,
      subscriptionId: response.data.data?.subscriptionId,
      data: response.data
    };

  } catch (error) {
    console.log(`❌ FAILED:`);
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error:', error.response?.data?.message || error.message);
    console.log('📊 Full Response:', JSON.stringify(error.response?.data, null, 2));
    
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// Test directly via Payment Microservice
async function testViaPaymentMicroservice(userData, planId, attemptNumber) {
  console.log(`\n📝 Attempt ${attemptNumber}: Testing via Payment Microservice`);
  console.log(`   Plan ID: ${planId}`);
  console.log(`   Email: ${userData.email}`);
  
  const token = generatePaymentJWT(userData.userId, 'com.gumbo.learning');
  
  const requestData = {
    userId: userData.userId,
    planId: planId,
    paymentContext: {
      metadata: {
        userName: userData.name,
        userEmail: userData.email,
        userPhone: userData.phone
      },
      subscriptionType: 'premium',
      billingCycle: 'monthly'
    }
  };

  console.log('🔗 Endpoint:', `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`);

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ SUCCESS (${responseTime}ms):`);
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('📊 Razorpay ID:', response.data.data?.razorpaySubscriptionId);
    
    return {
      success: true,
      responseTime,
      subscriptionId: response.data.data?.subscriptionId,
      data: response.data
    };

  } catch (error) {
    console.log(`❌ FAILED:`);
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error Code:', error.response?.data?.error?.code);
    console.log('📊 Error Message:', error.response?.data?.error?.message);
    
    return {
      success: false,
      error: error.response?.data,
      status: error.response?.status
    };
  }
}

// Main test to reproduce the issue
async function reproduceSecondSubscriptionIssue() {
  console.log('\n🚀 Starting Second Subscription Issue Test...\n');
  
  // Create a new test user
  const testUser = {
    userId: 'second_sub_test_' + Date.now(),
    name: 'Second Sub Test User',
    email: 'second.sub.test.' + Date.now() + '@example.com',
    phone: '9876543210'
  };
  
  console.log('👤 Test User Created:');
  console.log(`   User ID: ${testUser.userId}`);
  console.log(`   Email: ${testUser.email}`);
  console.log(`   Name: ${testUser.name}`);
  
  // Test 1: First subscription (should work)
  console.log('\n🔸 FIRST SUBSCRIPTION TEST:');
  const firstResult = await testViaSeekhoBackend(testUser, 'monthly', 1);
  
  if (!firstResult.success) {
    console.log('❌ First subscription failed - cannot test second subscription');
    return { success: false, reason: 'First subscription failed' };
  }
  
  // Wait a moment to ensure database persistence
  console.log('\n⏳ Waiting 3 seconds for database persistence...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test 2: Second subscription (this is where it should fail according to your report)
  console.log('\n🔸 SECOND SUBSCRIPTION TEST:');
  const secondResult = await testViaSeekhoBackend(testUser, 'yearly', 2);
  
  // Test 3: Direct payment microservice test for comparison
  console.log('\n🔸 DIRECT PAYMENT MICROSERVICE TEST:');
  const directResult = await testViaPaymentMicroservice(testUser, 'plan_QkkDw9QRHFT0nG', 3);
  
  // Analysis
  console.log('\n📊 SECOND SUBSCRIPTION ISSUE ANALYSIS:');
  console.log('=' .repeat(60));
  
  console.log(`🥇 First Subscription (Seekho-Backend): ${firstResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`🥈 Second Subscription (Seekho-Backend): ${secondResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`🎯 Direct Payment Microservice: ${directResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (firstResult.success && !secondResult.success) {
    console.log('\n🎯 ISSUE CONFIRMED: First works, second fails');
    console.log('🔍 This confirms the customer optimization issue');
    
    const firstTime = firstResult.responseTime;
    const secondError = secondResult.error?.message || 'Unknown error';
    
    console.log('\n📋 Issue Details:');
    console.log(`   First subscription time: ${firstTime}ms`);
    console.log(`   Second subscription error: ${secondError}`);
    
    console.log('\n🔧 Likely Causes:');
    console.log('1. Customer ID not being persisted correctly in database');
    console.log('2. Customer optimization query not finding existing customer');
    console.log('3. Race condition in customer creation/reuse logic');
    console.log('4. Database transaction not committed before second request');
    
    console.log('\n💡 Debugging Steps:');
    console.log('1. Check payment microservice logs for customer optimization');
    console.log('2. Verify subscription document has razorpayCustomerId');
    console.log('3. Check if getOrCreateCustomerId is finding existing customer');
    console.log('4. Test direct payment microservice to isolate issue');
    
  } else if (firstResult.success && secondResult.success) {
    console.log('\n🎉 ISSUE RESOLVED: Both subscriptions work');
    console.log('✅ Customer optimization is working correctly');
    
    const firstTime = firstResult.responseTime;
    const secondTime = secondResult.responseTime;
    const timeDifference = firstTime - secondTime;
    const isOptimized = secondTime < firstTime && timeDifference > 500;
    
    console.log('\n📊 Performance Analysis:');
    console.log(`   First subscription: ${firstTime}ms (customer creation)`);
    console.log(`   Second subscription: ${secondTime}ms (customer reuse)`);
    console.log(`   Time difference: ${timeDifference}ms`);
    console.log(`   Optimization working: ${isOptimized ? 'YES ✅' : 'NO ❌'}`);
    
  } else {
    console.log('\n❌ UNEXPECTED RESULT: First subscription failed');
    console.log('🔧 There may be a broader issue with the payment system');
  }
  
  return {
    firstSuccess: firstResult.success,
    secondSuccess: secondResult.success,
    directSuccess: directResult.success,
    issueConfirmed: firstResult.success && !secondResult.success
  };
}

// Run the test
reproduceSecondSubscriptionIssue().catch(console.error);
