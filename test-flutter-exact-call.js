#!/usr/bin/env node

/**
 * Test Exact Flutter API Call
 * 
 * This script replicates the exact API call that the Flutter app is making
 * to identify why it's failing with authentication.
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const PRODUCTION_BACKEND_URL = 'https://learner.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

console.log('🔍 Testing Exact Flutter API Call');
console.log('=' .repeat(60));

async function createValidUserInDB() {
  console.log('\n👤 Step 1: Creating a valid user in the database...');
  
  // This simulates what happens when a user logs in via Google OAuth
  // In production, this would be done through the actual Google OAuth flow
  
  const testUser = {
    id: 'flutter_test_user_123',
    email: '<EMAIL>',
    name: 'Flutter Test User',
    googleId: 'google_123456789',
    packageId: 'com.gumbo.learning'
  };
  
  console.log('📝 Test user data:', testUser);
  console.log('💡 Note: In production, this user would be created via Google OAuth');
  
  return testUser;
}

async function generateValidJWT(user) {
  console.log('\n🔑 Step 2: Generating valid JWT token...');
  
  const tokenPayload = {
    userId: user.id,
    email: user.email,
    name: user.name,
    packageId: user.packageId,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  const token = jwt.sign(tokenPayload, JWT_SECRET);
  
  console.log('✅ JWT token generated');
  console.log('🔍 Token payload:', {
    userId: tokenPayload.userId,
    email: tokenPayload.email,
    packageId: tokenPayload.packageId,
    expiresIn: '24 hours'
  });
  
  return token;
}

async function testCreateRegularSubscription(token) {
  console.log('\n💳 Step 3: Testing createRegularSubscription (exact Flutter call)...');
  
  // This replicates the exact call from Flutter's createRegularSubscription method
  const requestBody = {
    plan: 'monthly',
    recurring: true,
    name: 'Flutter Test User',
    email: '<EMAIL>',
    phone: '9403012499'
  };
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'x-package-id': 'com.gumbo.learning',
  };
  
  console.log('📡 Making API call to: /api/subscriptions/create-order');
  console.log('📋 Request body:', requestBody);
  console.log('🔑 Headers:', {
    'Content-Type': headers['Content-Type'],
    'Authorization': `Bearer ${token.substring(0, 20)}...`,
    'x-package-id': headers['x-package-id']
  });
  
  try {
    const response = await axios.post(
      `${PRODUCTION_BACKEND_URL}/api/subscriptions/create-order`,
      requestBody,
      { headers }
    );
    
    console.log('✅ Subscription creation successful!');
    console.log('📡 Response status:', response.status);
    console.log('📋 Response data:', JSON.stringify(response.data, null, 2));
    
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ Subscription creation failed:');
    console.log('📡 Response status:', error.response?.status);
    console.log('📋 Response data:', JSON.stringify(error.response?.data, null, 2));
    
    // Analyze the specific error
    if (error.response?.status === 401) {
      console.log('🔍 401 Error Analysis:');
      console.log('  - Token format appears correct');
      console.log('  - Issue might be: user not found in database');
      console.log('  - Or: JWT secret mismatch');
      console.log('  - Or: token payload structure mismatch');
    } else if (error.response?.status === 500) {
      console.log('🔍 500 Error Analysis:');
      console.log('  - Authentication likely worked');
      console.log('  - Issue is in subscription creation logic');
      console.log('  - Check backend logs for detailed error');
    }
    
    return { success: false, error: error.response?.data };
  }
}

async function testWithRealUserFlow() {
  console.log('\n🔄 Step 4: Testing with simulated real user flow...');
  
  // Simulate the Google OAuth flow by making a request to the auth endpoint
  try {
    console.log('📡 Testing Google OAuth endpoint...');
    
    const authResponse = await axios.get(
      `${PRODUCTION_BACKEND_URL}/api/auth/android/google`,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Google OAuth endpoint accessible');
    
  } catch (error) {
    if (error.response?.status === 302 || error.response?.headers?.location) {
      console.log('✅ Google OAuth redirect working (expected)');
    } else {
      console.log('🔍 Google OAuth response:', error.response?.status);
    }
  }
  
  console.log('💡 For real testing, user needs to complete Google OAuth flow');
  console.log('💡 This would create the user in the database and provide a valid token');
}

async function runExactFlutterTest() {
  try {
    // Step 1: Create test user (simulates Google OAuth)
    const testUser = await createValidUserInDB();
    
    // Step 2: Generate JWT token
    const token = await generateValidJWT(testUser);
    
    // Step 3: Test subscription creation
    const result = await testCreateRegularSubscription(token);
    
    // Step 4: Test real user flow
    await testWithRealUserFlow();
    
    console.log('\n📊 Test Summary:');
    if (result.success) {
      console.log('✅ Flutter app authentication is working correctly');
      console.log('✅ The issue was likely a temporary backend problem');
    } else {
      console.log('❌ Flutter app authentication has issues');
      console.log('🔍 Root cause identified in the test above');
    }
    
    console.log('\n💡 Recommendations:');
    console.log('1. Ensure user completes Google OAuth flow in Flutter app');
    console.log('2. Verify JWT token is stored correctly after authentication');
    console.log('3. Check that user exists in production database');
    console.log('4. Verify backend JWT secret matches token generation');
    
  } catch (error) {
    console.error('💥 Test execution failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  runExactFlutterTest()
    .then(() => {
      console.log('\n🎯 Exact Flutter test completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { runExactFlutterTest };
