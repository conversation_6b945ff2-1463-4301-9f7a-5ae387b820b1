#!/usr/bin/env node

/**
 * Test to verify the amount format fix works
 * This should now work for both one-time orders and recurring subscriptions
 */

const axios = require('axios');

const SEEKHO_BACKEND_URL = 'https://learner.netaapp.in';

console.log('🧪 Testing Amount Format Fix Verification');
console.log('=' .repeat(60));
console.log('🎯 Verifying one-time orders and recurring subscriptions work');

// Register a test user and get token
async function registerAndGetToken() {
  console.log('\n📝 Step 1: Registering Test User');
  
  const testUser = {
    name: 'Amount Fix Test User',
    email: 'amount.fix.test.' + Date.now() + '@example.com',
    phone: '9876543210',
    password: 'TestPassword123!',
    packageId: 'com.gumbo.learning'
  };
  
  console.log(`👤 Test User: ${testUser.email}`);
  
  try {
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/auth/register`,
      testUser,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('✅ User registration SUCCESS');
    
    return { 
      success: true, 
      token: response.data.data?.token,
      testUser 
    };

  } catch (error) {
    console.log('❌ User registration FAILED:', error.response?.data?.message);
    return { success: false };
  }
}

// Test one-time order (the main fix)
async function testOneTimeOrder(authData) {
  console.log('\n📝 Step 2: Testing One-Time Order (Main Fix)');
  
  if (!authData.success) {
    console.log('❌ Cannot test - no authentication');
    return { success: false };
  }
  
  const { token, testUser } = authData;
  
  const requestData = {
    plan: 'monthly',
    recurring: false, // One-time order
    name: testUser.name,
    email: testUser.email,
    phone: testUser.phone
  };

  console.log('🔗 Endpoint:', `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`);
  console.log('📋 Request:', JSON.stringify(requestData, null, 2));

  try {
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('\n✅ One-time order SUCCESS:');
    console.log('📊 Order ID:', response.data.data?.orderId);
    console.log('📊 Amount:', response.data.data?.amount);
    console.log('📊 Currency:', response.data.data?.currency);
    console.log('📊 Type:', response.data.data?.type);
    console.log('📊 Razorpay Key:', response.data.data?.razorpayKeyId ? 'Present' : 'Missing');
    
    return { success: true, data: response.data };

  } catch (error) {
    console.log('\n❌ One-time order FAILED:');
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error:', error.response?.data?.message);
    console.log('📊 Full Error:', JSON.stringify(error.response?.data, null, 2));
    
    return { success: false, error: error.response?.data };
  }
}

// Test recurring subscription
async function testRecurringSubscription(authData) {
  console.log('\n📝 Step 3: Testing Recurring Subscription');
  
  if (!authData.success) {
    console.log('❌ Cannot test - no authentication');
    return { success: false };
  }
  
  const { token, testUser } = authData;
  
  const requestData = {
    plan: 'monthly',
    recurring: true, // Recurring subscription
    name: testUser.name,
    email: testUser.email,
    phone: testUser.phone
  };

  console.log('📋 Request:', JSON.stringify(requestData, null, 2));

  try {
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('\n✅ Recurring subscription SUCCESS:');
    console.log('📊 Subscription ID:', response.data.data?.subscriptionId);
    console.log('📊 Razorpay Sub ID:', response.data.data?.razorpaySubscriptionId);
    console.log('📊 Status:', response.data.data?.status);
    console.log('📊 Type:', response.data.data?.type);
    
    return { success: true, data: response.data };

  } catch (error) {
    console.log('\n❌ Recurring subscription FAILED:');
    console.log('📊 Status:', error.response?.status);
    console.log('📊 Error:', error.response?.data?.message);
    
    return { success: false, error: error.response?.data };
  }
}

// Test yearly plan
async function testYearlyPlan(authData) {
  console.log('\n📝 Step 4: Testing Yearly Plan');
  
  if (!authData.success) {
    console.log('❌ Cannot test - no authentication');
    return { success: false };
  }
  
  const { token, testUser } = authData;
  
  const requestData = {
    plan: 'yearly',
    recurring: false, // One-time yearly order
    name: testUser.name,
    email: testUser.email,
    phone: testUser.phone
  };

  try {
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
        },
        timeout: 30000
      }
    );

    console.log('\n✅ Yearly plan SUCCESS:');
    console.log('📊 Order ID:', response.data.data?.orderId);
    console.log('📊 Amount:', response.data.data?.amount);
    
    return { success: true, data: response.data };

  } catch (error) {
    console.log('\n❌ Yearly plan FAILED:');
    console.log('📊 Error:', error.response?.data?.message);
    
    return { success: false, error: error.response?.data };
  }
}

// Main test execution
async function runAmountFixVerification() {
  console.log('\n🚀 Starting Amount Fix Verification Test...\n');
  
  // Step 1: Get authentication
  const authData = await registerAndGetToken();
  
  // Step 2: Test one-time order (main fix)
  const oneTimeResult = await testOneTimeOrder(authData);
  
  // Step 3: Test recurring subscription
  const recurringResult = await testRecurringSubscription(authData);
  
  // Step 4: Test yearly plan
  const yearlyResult = await testYearlyPlan(authData);
  
  // Analysis
  console.log('\n📊 AMOUNT FIX VERIFICATION RESULTS:');
  console.log('=' .repeat(60));
  
  console.log(`🔸 User Authentication: ${authData.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`🔸 One-Time Order (Monthly): ${oneTimeResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`🔸 Recurring Subscription: ${recurringResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`🔸 Yearly Plan: ${yearlyResult.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  const allWorking = authData.success && oneTimeResult.success && recurringResult.success && yearlyResult.success;
  
  if (allWorking) {
    console.log('\n🎉 AMOUNT FIX SUCCESSFUL - ALL SYSTEMS WORKING!');
    console.log('✅ One-time orders working (main fix applied)');
    console.log('✅ Recurring subscriptions working');
    console.log('✅ Both monthly and yearly plans working');
    console.log('✅ Payment amounts correctly sent in paise format');
    
    console.log('\n📱 Android App Status:');
    console.log('✅ Should work perfectly now');
    console.log('✅ Both one-time and recurring payments enabled');
    console.log('✅ All plan types (monthly/yearly) functional');
    
    console.log('\n📋 Next Steps:');
    console.log('1. ✅ Deploy to production (already done)');
    console.log('2. ✅ Test Android app end-to-end');
    console.log('3. ✅ Monitor production logs for success');
    console.log('4. ✅ Verify Razorpay dashboard for orders');
    
  } else if (authData.success && oneTimeResult.success && !recurringResult.success) {
    console.log('\n🎉 ONE-TIME ORDER FIX SUCCESSFUL!');
    console.log('✅ Main issue resolved - one-time orders working');
    console.log('⚠️ Recurring subscriptions still have issues');
    console.log('🔧 May need additional fixes for subscription flow');
    
  } else if (authData.success && !oneTimeResult.success) {
    console.log('\n⚠️ AMOUNT FIX NOT FULLY EFFECTIVE:');
    console.log('✅ Authentication working');
    console.log('❌ One-time orders still failing');
    console.log('🔧 May need additional debugging');
    
    if (oneTimeResult.error) {
      console.log('\n📋 One-Time Order Error Details:');
      console.log(`   Message: ${oneTimeResult.error.message || 'Unknown'}`);
      
      if (oneTimeResult.error.message?.includes('validation')) {
        console.log('   🔧 Still a validation issue - check payment microservice');
      } else if (oneTimeResult.error.message?.includes('customer')) {
        console.log('   🔧 Customer creation issue - check Razorpay integration');
      }
    }
    
  } else {
    console.log('\n❌ AUTHENTICATION ISSUE:');
    console.log('❌ Cannot test payment flows without authentication');
    console.log('🔧 Check user registration and JWT token generation');
  }
  
  console.log('\n📊 Summary:');
  console.log(`   Success Rate: ${[oneTimeResult.success, recurringResult.success, yearlyResult.success].filter(Boolean).length}/3`);
  console.log(`   Critical Fix (One-Time): ${oneTimeResult.success ? 'WORKING ✅' : 'FAILING ❌'}`);
  
  return {
    fixSuccessful: allWorking,
    oneTimeFixed: oneTimeResult.success,
    recurringWorking: recurringResult.success,
    allPaymentTypesWorking: allWorking
  };
}

// Run the verification test
runAmountFixVerification().catch(console.error);
