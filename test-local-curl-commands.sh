#!/bin/bash

# Local Subscription Creation Test - cURL Commands
# This script tests the exact same flow as production but against local servers

echo "🧪 Testing LOCAL Subscription Creation with cURL"
echo "============================================================"

# Configuration
LOCAL_URL="http://localhost:5001"
PACKAGE_ID="com.gumbo.learning"

# <PERSON><PERSON><PERSON>'s user data (from database)
USER_ID="688866c9baa54c07f2616aaf"
USER_EMAIL="<EMAIL>"
USER_NAME="Praveen Singh"

# Generate JWT token for <PERSON><PERSON><PERSON> (using the correct format)
echo "🔑 Step 1: Generating JWT token for Praveen..."

# Generate the token
cat > generate_token.js << 'EOF'
const jwt = require('jsonwebtoken');
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

const tokenPayload = {
  id: '688866c9baa54c07f2616aaf', // <PERSON><PERSON><PERSON>'s user ID
  email: '<EMAIL>',
  name: '<PERSON><PERSON><PERSON>',
  packageId: 'com.gumbo.learning',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
};

const token = jwt.sign(tokenPayload, JWT_SECRET);
console.log(token);
EOF

JWT_TOKEN=$(node generate_token.js)
echo "✅ JWT Token generated: ${JWT_TOKEN:0:50}..."

echo ""
echo "🚀 Running LOCAL Tests..."
echo "============================================================"

echo ""
echo "🔍 Test 1: Local Subscription Plans"
echo "curl -X GET '$LOCAL_URL/api/subscriptions/plans' -H 'x-package-id: $PACKAGE_ID'"
PLANS_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X GET "$LOCAL_URL/api/subscriptions/plans" \
  -H 'Content-Type: application/json' \
  -H "x-package-id: $PACKAGE_ID")

PLANS_STATUS=$(echo "$PLANS_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
PLANS_BODY=$(echo "$PLANS_RESPONSE" | sed '/HTTP_STATUS:/d')

echo "📡 HTTP Status: $PLANS_STATUS"
echo "📋 Response:"
echo "$PLANS_BODY" | jq '.' 2>/dev/null || echo "$PLANS_BODY"

echo ""
echo "🔍 Test 2: Local Subscription Status"
echo "curl -X GET '$LOCAL_URL/api/subscriptions/status' -H 'Authorization: Bearer ...' -H 'x-package-id: $PACKAGE_ID'"
STATUS_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X GET "$LOCAL_URL/api/subscriptions/status" \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-package-id: $PACKAGE_ID")

STATUS_HTTP=$(echo "$STATUS_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
STATUS_BODY=$(echo "$STATUS_RESPONSE" | sed '/HTTP_STATUS:/d')

echo "📡 HTTP Status: $STATUS_HTTP"
echo "📋 Response:"
echo "$STATUS_BODY" | jq '.' 2>/dev/null || echo "$STATUS_BODY"

echo ""
echo "💳 Test 3: Local Monthly Subscription Creation"
echo "curl -X POST '$LOCAL_URL/api/subscriptions/create-order' ..."
MONTHLY_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X POST "$LOCAL_URL/api/subscriptions/create-order" \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-package-id: $PACKAGE_ID" \
  -d "{
    \"plan\": \"monthly\",
    \"recurring\": true,
    \"name\": \"$USER_NAME\",
    \"email\": \"$USER_EMAIL\",
    \"phone\": \"9876543210\"
  }")

MONTHLY_STATUS=$(echo "$MONTHLY_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
MONTHLY_BODY=$(echo "$MONTHLY_RESPONSE" | sed '/HTTP_STATUS:/d')

echo "📡 HTTP Status: $MONTHLY_STATUS"
echo "📋 Response:"
echo "$MONTHLY_BODY" | jq '.' 2>/dev/null || echo "$MONTHLY_BODY"

if [ "$MONTHLY_STATUS" = "200" ]; then
    echo "✅ LOCAL Monthly subscription: SUCCESS"
else
    echo "❌ LOCAL Monthly subscription: FAILED (Status: $MONTHLY_STATUS)"
fi

echo ""
echo "💳 Test 4: Local Yearly Subscription Creation"
echo "curl -X POST '$LOCAL_URL/api/subscriptions/create-order' ..."
YEARLY_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X POST "$LOCAL_URL/api/subscriptions/create-order" \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-package-id: $PACKAGE_ID" \
  -d "{
    \"plan\": \"yearly\",
    \"recurring\": true,
    \"name\": \"$USER_NAME\",
    \"email\": \"$USER_EMAIL\",
    \"phone\": \"9876543210\"
  }")

YEARLY_STATUS=$(echo "$YEARLY_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
YEARLY_BODY=$(echo "$YEARLY_RESPONSE" | sed '/HTTP_STATUS:/d')

echo "📡 HTTP Status: $YEARLY_STATUS"
echo "📋 Response:"
echo "$YEARLY_BODY" | jq '.' 2>/dev/null || echo "$YEARLY_BODY"

if [ "$YEARLY_STATUS" = "200" ]; then
    echo "✅ LOCAL Yearly subscription: SUCCESS"
else
    echo "❌ LOCAL Yearly subscription: FAILED (Status: $YEARLY_STATUS)"
fi

echo ""
echo "📊 LOCAL vs PRODUCTION Comparison"
echo "============================================================"
echo "LOCAL Results:"
echo "  Plans API: $([ "$PLANS_STATUS" = "200" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  Subscription Status: $([ "$STATUS_HTTP" = "200" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  Monthly Subscription: $([ "$MONTHLY_STATUS" = "200" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  Yearly Subscription: $([ "$YEARLY_STATUS" = "200" ] && echo "✅ PASS" || echo "❌ FAIL")"

echo ""
echo "PRODUCTION Results (from previous test):"
echo "  Plans API: ✅ PASS"
echo "  Subscription Status: ✅ PASS"
echo "  Monthly Subscription: ❌ FAIL (500 error)"
echo "  Yearly Subscription: ❌ FAIL (500 error)"

echo ""
if [ "$MONTHLY_STATUS" = "200" ] && [ "$YEARLY_STATUS" = "200" ]; then
    echo "🎉 LOCAL SERVERS WORK PERFECTLY!"
    echo "💡 This proves the issue is in PRODUCTION deployment"
    echo ""
    echo "🔧 PRODUCTION ISSUES TO CHECK:"
    echo "1. Payment microservice URL in production"
    echo "2. Payment microservice running in production"
    echo "3. Environment variables in production"
    echo "4. Code deployment in production"
    echo "5. Database connectivity in production"
else
    echo "❌ LOCAL SERVERS ALSO HAVE ISSUES"
    echo "💡 Need to fix local issues first"
fi

echo ""
echo "🎯 Next Steps:"
if [ "$MONTHLY_STATUS" = "200" ] && [ "$YEARLY_STATUS" = "200" ]; then
    echo "1. ✅ Local servers work - focus on production deployment"
    echo "2. Check production payment microservice URL"
    echo "3. Verify production environment variables"
    echo "4. Restart production services"
else
    echo "1. ❌ Fix local server issues first"
    echo "2. Check local payment microservice integration"
    echo "3. Verify local environment variables"
fi

# Cleanup
rm -f generate_token.js

echo ""
echo "🎯 Local cURL testing completed!"
