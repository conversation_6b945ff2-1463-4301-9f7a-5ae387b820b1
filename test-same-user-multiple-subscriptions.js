#!/usr/bin/env node

/**
 * Test Same User Multiple Subscriptions
 * 
 * This test verifies that when the EXACT same user creates multiple subscriptions,
 * the system correctly reuses the customer ID and prevents duplicate customer creation.
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

// Configuration
const PAYMENT_MICROSERVICE_URL = 'http://localhost:3000';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

// Fixed test user (same user for all subscriptions)
const FIXED_TEST_USER = {
  id: 'fixed_user_12345',
  name: 'Fixed Test User',
  email: '<EMAIL>',
  phone: '9876543210',
  packageId: 'com.gumbo.learning'
};

const TEST_PLANS = {
  monthly: 'plan_QkkDaTp9Hje6uC',
  yearly: 'plan_QkkDw9QRHFT0nG'
};

console.log('🔄 Testing Same User Multiple Subscriptions');
console.log('=' .repeat(60));
console.log(`👤 Fixed User ID: ${FIXED_TEST_USER.id}`);
console.log(`📧 Email: ${FIXED_TEST_USER.email}`);

async function generateJWT(userId, packageId) {
  return jwt.sign(
    { 
      userId, 
      appId: packageId 
    },
    JWT_SECRET,
    { expiresIn: '1h' }
  );
}

async function createSubscription(planType, planId) {
  try {
    const token = await generateJWT(FIXED_TEST_USER.id, FIXED_TEST_USER.packageId);
    
    const subscriptionData = {
      userId: FIXED_TEST_USER.id,
      planId: planId,
      paymentContext: {
        subscriptionType: 'premium',
        billingCycle: planType,
        recurring: true,
        metadata: {
          userName: FIXED_TEST_USER.name,
          userEmail: FIXED_TEST_USER.email,
          userPhone: FIXED_TEST_USER.phone,
          userId: FIXED_TEST_USER.id,
          packageId: FIXED_TEST_USER.packageId
        }
      }
    };

    console.log(`\n📤 Creating ${planType} subscription...`);
    console.log(`Plan ID: ${planId}`);

    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      subscriptionData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': FIXED_TEST_USER.packageId
        }
      }
    );

    console.log(`✅ ${planType} subscription created successfully:`);
    console.log(`  Subscription ID: ${response.data.data.subscriptionId}`);
    console.log(`  Razorpay Subscription ID: ${response.data.data.razorpaySubscriptionId}`);
    console.log(`  Razorpay Customer ID: ${response.data.data.razorpayCustomerId}`);
    console.log(`  Status: ${response.data.data.status}`);

    return {
      success: true,
      data: response.data.data
    };

  } catch (error) {
    console.error(`❌ ${planType} subscription creation failed:`);
    console.error(`  Error: ${error.response?.data?.error?.message || error.message}`);
    
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

async function runTest() {
  console.log('\n🚀 Starting same user multiple subscription test...');

  // Create first subscription (monthly)
  const monthlyResult = await createSubscription('monthly', TEST_PLANS.monthly);
  
  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Create second subscription (yearly) - same user
  const yearlyResult = await createSubscription('yearly', TEST_PLANS.yearly);
  
  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Create third subscription (monthly again) - same user
  const monthly2Result = await createSubscription('monthly-2', TEST_PLANS.monthly);

  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(60));
  
  const allSuccessful = monthlyResult.success && yearlyResult.success && monthly2Result.success;
  
  if (allSuccessful) {
    console.log('✅ All subscriptions created successfully!');
    
    // Check if customer IDs are the same
    const customerIds = [
      monthlyResult.data.razorpayCustomerId,
      yearlyResult.data.razorpayCustomerId,
      monthly2Result.data.razorpayCustomerId
    ];
    
    const uniqueCustomerIds = [...new Set(customerIds)];
    
    if (uniqueCustomerIds.length === 1) {
      console.log('✅ Customer ID reuse working correctly!');
      console.log(`   All subscriptions use the same customer ID: ${uniqueCustomerIds[0]}`);
    } else {
      console.log('❌ Customer ID reuse failed!');
      console.log('   Different customer IDs found:', uniqueCustomerIds);
    }
    
    console.log('\n🔍 Subscription Details:');
    console.log(`1. Monthly: ${monthlyResult.data.razorpaySubscriptionId} (Customer: ${monthlyResult.data.razorpayCustomerId})`);
    console.log(`2. Yearly:  ${yearlyResult.data.razorpaySubscriptionId} (Customer: ${yearlyResult.data.razorpayCustomerId})`);
    console.log(`3. Monthly-2: ${monthly2Result.data.razorpaySubscriptionId} (Customer: ${monthly2Result.data.razorpayCustomerId})`);
    
  } else {
    console.log('❌ Some subscriptions failed to create');
    if (!monthlyResult.success) console.log('  Monthly failed:', monthlyResult.error);
    if (!yearlyResult.success) console.log('  Yearly failed:', yearlyResult.error);
    if (!monthly2Result.success) console.log('  Monthly-2 failed:', monthly2Result.error);
  }

  return allSuccessful;
}

// Run the test
if (require.main === module) {
  runTest()
    .then(success => {
      console.log(`\n🎯 Final Result: ${success ? '✅ TEST PASSED' : '❌ TEST FAILED'}`);
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { runTest };
