#!/usr/bin/env node

/**
 * Debug User Creation in Production
 * 
 * This script helps debug why users authenticated via Flutter app
 * are not found in the database when making subscription requests.
 */

const axios = require('axios');
const { MongoClient } = require('mongodb');

const PRODUCTION_BACKEND_URL = 'https://learner.netaapp.in';
const SEEKHO_DB_URI = 'mongodb+srv://prav59632:<EMAIL>/';

console.log('🔍 Debugging User Creation in Production');
console.log('=' .repeat(60));

async function checkUsersInDatabase() {
  console.log('\n📊 Step 1: Checking users in production database...');
  
  const client = new MongoClient(SEEKHO_DB_URI);
  
  try {
    await client.connect();
    const db = client.db();
    const usersCollection = db.collection('users');
    
    // Get recent users (last 10)
    const recentUsers = await usersCollection
      .find({})
      .sort({ createdAt: -1 })
      .limit(10)
      .toArray();
    
    console.log(`✅ Found ${recentUsers.length} recent users in database:`);
    
    recentUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (ID: ${user._id})`);
      console.log(`   Created: ${user.createdAt || 'Unknown'}`);
      console.log(`   Google ID: ${user.googleId || 'None'}`);
      console.log(`   Package ID: ${user.packageId || 'None'}`);
      console.log('');
    });
    
    // Check for users with specific patterns
    const googleUsers = await usersCollection
      .find({ googleId: { $exists: true, $ne: null } })
      .limit(5)
      .toArray();
    
    console.log(`🔍 Found ${googleUsers.length} users with Google authentication:`);
    googleUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (Google ID: ${user.googleId})`);
    });
    
  } catch (error) {
    console.error('❌ Error checking database:', error.message);
  } finally {
    await client.close();
  }
}

async function testGoogleAuthEndpoint() {
  console.log('\n🔐 Step 2: Testing Google Auth endpoint...');
  
  try {
    // Test the Android Google Auth endpoint
    const response = await axios.get(
      `${PRODUCTION_BACKEND_URL}/api/auth/android/google`,
      {
        headers: {
          'Content-Type': 'application/json'
        },
        maxRedirects: 0, // Don't follow redirects
        validateStatus: function (status) {
          return status < 500; // Accept any status less than 500
        }
      }
    );
    
    console.log('📡 Response status:', response.status);
    console.log('📋 Response headers:', response.headers);
    
  } catch (error) {
    if (error.response?.status === 302) {
      console.log('✅ Google OAuth redirect working (expected)');
      console.log('🔗 Redirect URL:', error.response.headers.location);
    } else {
      console.log('❌ Google Auth endpoint error:', error.response?.status);
    }
  }
}

async function testAndroidConfigEndpoint() {
  console.log('\n⚙️ Step 3: Testing Android config endpoint...');
  
  try {
    const response = await axios.get(
      `${PRODUCTION_BACKEND_URL}/api/auth/android/config`,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Android config endpoint working');
    console.log('📋 Config data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ Android config endpoint error:', error.response?.status);
    console.log('📋 Error data:', error.response?.data);
  }
}

async function simulateUserCreation() {
  console.log('\n👤 Step 4: Simulating user creation process...');
  
  // This shows what should happen when a user authenticates via Google
  console.log('💡 Normal Google OAuth flow:');
  console.log('1. User clicks "Sign in with Google" in Flutter app');
  console.log('2. Flutter app gets Google ID token');
  console.log('3. Flutter app sends ID token to /api/auth/android/google');
  console.log('4. Backend verifies Google ID token');
  console.log('5. Backend creates/updates user in database');
  console.log('6. Backend returns JWT token to Flutter app');
  console.log('7. Flutter app stores JWT token');
  console.log('8. Flutter app uses JWT token for subsequent API calls');
  
  console.log('\n🔍 Potential issues:');
  console.log('❌ Step 5 might be failing - user not created in database');
  console.log('❌ Step 6 might be failing - JWT token contains wrong user ID');
  console.log('❌ Step 7 might be failing - token not stored properly');
  console.log('❌ Step 8 might be failing - token not sent with requests');
}

async function checkJWTTokenStructure() {
  console.log('\n🔑 Step 5: Checking JWT token structure...');
  
  const jwt = require('jsonwebtoken');
  const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';
  
  // Create a sample token like the backend would
  const samplePayload = {
    userId: 'sample_user_id',
    email: '<EMAIL>',
    name: 'Sample User',
    packageId: 'com.gumbo.learning',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
  };
  
  const sampleToken = jwt.sign(samplePayload, JWT_SECRET);
  
  console.log('✅ Sample JWT token structure:');
  console.log('📋 Payload:', samplePayload);
  console.log('🔍 Token length:', sampleToken.length);
  
  // Verify the token
  try {
    const decoded = jwt.verify(sampleToken, JWT_SECRET);
    console.log('✅ Token verification successful');
    console.log('📋 Decoded payload:', decoded);
  } catch (error) {
    console.log('❌ Token verification failed:', error.message);
  }
}

async function runUserCreationDebug() {
  try {
    await checkUsersInDatabase();
    await testGoogleAuthEndpoint();
    await testAndroidConfigEndpoint();
    await simulateUserCreation();
    await checkJWTTokenStructure();
    
    console.log('\n📊 Debug Summary:');
    console.log('✅ Database connection working');
    console.log('✅ Authentication endpoints accessible');
    console.log('🔍 Issue is likely in the Google OAuth user creation flow');
    
    console.log('\n💡 Next Steps:');
    console.log('1. Check if users are being created when they authenticate via Flutter');
    console.log('2. Verify JWT tokens contain correct user IDs that exist in database');
    console.log('3. Test the complete Google OAuth flow end-to-end');
    console.log('4. Check backend logs during user authentication');
    
  } catch (error) {
    console.error('💥 Debug execution failed:', error.message);
  }
}

// Run the debug
if (require.main === module) {
  runUserCreationDebug()
    .then(() => {
      console.log('\n🎯 User creation debug completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Debug failed:', error);
      process.exit(1);
    });
}

module.exports = { runUserCreationDebug };
