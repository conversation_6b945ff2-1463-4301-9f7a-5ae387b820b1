#!/usr/bin/env node

/**
 * Integration Test Script - Post Deployment
 * Tests the complete Flutter → Seekho-Backend → Payment Microservice flow
 * Run this after deploying the customer creation fix
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

// Configuration from real Android app testing
const SEEKHO_BACKEND_URL = 'https://learner.netaapp.in';
const PAYMENT_MICROSERVICE_URL = 'https://payments.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

// Real user data from Android app logs
const REAL_USER_DATA = {
  name: 'praveen',
  email: '<EMAIL>',
  phone: '9403012499',
  packageId: 'com.gumbo.learning'
};

console.log('🧪 Multi-Service Integration Test - Post Deployment');
console.log('=' .repeat(70));
console.log('📱 Flutter App → 🖥️ Seekho-Backend → 💳 Payment Microservice');
console.log('');
console.log('🎯 Testing with real user data from Android app:');
console.log(`   Name: ${REAL_USER_DATA.name}`);
console.log(`   Email: ${REAL_USER_DATA.email}`);
console.log(`   Phone: ${REAL_USER_DATA.phone}`);
console.log(`   Package: ${REAL_USER_DATA.packageId}`);

// Generate JWT token for seekho-backend
function generateSeekhoJWT(userData) {
  return jwt.sign(
    {
      id: 'test_user_integration_' + Date.now(),
      email: userData.email,
      name: userData.name,
      phone: userData.phone
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
}

// Generate JWT token for payment microservice
function generatePaymentJWT(userId, packageId) {
  return jwt.sign(
    {
      userId: userId,
      appId: packageId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    },
    JWT_SECRET
  );
}

// Test 1: Flutter → Seekho-Backend Integration
async function testFlutterToSeekhoBackend() {
  console.log('\n📝 Test 1: Flutter → Seekho-Backend Integration');
  console.log('=' .repeat(50));
  
  const token = generateSeekhoJWT(REAL_USER_DATA);
  
  const requestData = {
    plan: 'monthly',
    recurring: true,
    name: REAL_USER_DATA.name,
    email: REAL_USER_DATA.email,
    phone: REAL_USER_DATA.phone
  };

  console.log('🔗 Endpoint:', `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`);
  console.log('📋 Request:', JSON.stringify(requestData, null, 2));

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${SEEKHO_BACKEND_URL}/api/subscriptions/create-order`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-package-id': REAL_USER_DATA.packageId
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ SUCCESS (${responseTime}ms):`);
    console.log('📊 Response:', JSON.stringify(response.data, null, 2));
    
    return {
      success: true,
      responseTime,
      data: response.data
    };

  } catch (error) {
    console.log('❌ FAILED:');
    console.log('   Status:', error.response?.status);
    console.log('   Error:', error.response?.data?.message || error.message);
    console.log('   Full Response:', JSON.stringify(error.response?.data, null, 2));
    
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// Test 2: Direct Payment Microservice Test
async function testPaymentMicroserviceDirect() {
  console.log('\n📝 Test 2: Direct Payment Microservice Test');
  console.log('=' .repeat(50));
  
  const userId = 'integration_test_' + Date.now();
  const token = generatePaymentJWT(userId, REAL_USER_DATA.packageId);
  
  const requestData = {
    userId: userId,
    planId: 'plan_QkkDaTp9Hje6uC', // Monthly plan from Android logs
    paymentContext: {
      metadata: {
        userName: REAL_USER_DATA.name,
        userEmail: REAL_USER_DATA.email,
        userPhone: REAL_USER_DATA.phone
      },
      subscriptionType: 'premium',
      billingCycle: 'monthly'
    }
  };

  console.log('🔗 Endpoint:', `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`);
  console.log('📋 Request:', JSON.stringify(requestData, null, 2));

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': REAL_USER_DATA.packageId
        },
        timeout: 30000
      }
    );

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ SUCCESS (${responseTime}ms):`);
    console.log('📊 Response:', JSON.stringify(response.data, null, 2));
    
    return {
      success: true,
      responseTime,
      data: response.data
    };

  } catch (error) {
    console.log('❌ FAILED:');
    console.log('   Status:', error.response?.status);
    console.log('   Code:', error.response?.data?.error?.code);
    console.log('   Message:', error.response?.data?.error?.message);
    console.log('   Full Response:', JSON.stringify(error.response?.data, null, 2));
    
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// Test 3: Customer Optimization Test
async function testCustomerOptimization() {
  console.log('\n📝 Test 3: Customer Optimization Test');
  console.log('=' .repeat(50));
  
  const userId = 'optimization_test_' + Date.now();
  const testEmail = 'optimization.test.' + Date.now() + '@example.com';
  
  console.log(`👤 Test User: ${userId}`);
  console.log(`📧 Test Email: ${testEmail}`);
  
  // First request - should create customer
  console.log('\n🔸 First Request (Customer Creation):');
  const firstResult = await testPaymentMicroserviceWithData(userId, testEmail, 'monthly');
  
  if (!firstResult.success) {
    console.log('❌ First request failed, cannot test optimization');
    return { success: false, reason: 'First request failed' };
  }
  
  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Second request - should reuse customer
  console.log('\n🔸 Second Request (Customer Reuse):');
  const secondResult = await testPaymentMicroserviceWithData(userId, testEmail, 'yearly');
  
  if (!secondResult.success) {
    console.log('❌ Second request failed');
    return { success: false, reason: 'Second request failed' };
  }
  
  // Analyze optimization
  const firstTime = firstResult.responseTime;
  const secondTime = secondResult.responseTime;
  const timeDifference = firstTime - secondTime;
  const isOptimized = secondTime < firstTime && timeDifference > 500;
  
  console.log('\n📊 Optimization Analysis:');
  console.log(`   First request: ${firstTime}ms (customer creation)`);
  console.log(`   Second request: ${secondTime}ms (customer reuse)`);
  console.log(`   Time difference: ${timeDifference}ms`);
  console.log(`   Optimization working: ${isOptimized ? 'YES ✅' : 'NO ❌'}`);
  
  return {
    success: true,
    firstTime,
    secondTime,
    timeDifference,
    isOptimized
  };
}

// Helper function for optimization test
async function testPaymentMicroserviceWithData(userId, email, planType) {
  const token = generatePaymentJWT(userId, REAL_USER_DATA.packageId);
  const planId = planType === 'monthly' ? 'plan_QkkDaTp9Hje6uC' : 'plan_QkkDw9QRHFT0nG';
  
  const requestData = {
    userId: userId,
    planId: planId,
    paymentContext: {
      metadata: {
        userName: 'Optimization Test User',
        userEmail: email,
        userPhone: '9876543210'
      }
    }
  };

  try {
    const startTime = Date.now();
    
    const response = await axios.post(
      `${PAYMENT_MICROSERVICE_URL}/api/payment/subscription`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'x-app-id': REAL_USER_DATA.packageId
        },
        timeout: 20000
      }
    );

    const endTime = Date.now();
    return {
      success: true,
      responseTime: endTime - startTime,
      data: response.data
    };

  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

// Main test execution
async function runIntegrationTests() {
  console.log('\n🚀 Starting Multi-Service Integration Tests...\n');
  
  const results = {
    flutterToSeekho: null,
    paymentMicroservice: null,
    customerOptimization: null
  };
  
  // Test 1: Flutter → Seekho-Backend
  results.flutterToSeekho = await testFlutterToSeekhoBackend();
  
  // Test 2: Direct Payment Microservice
  results.paymentMicroservice = await testPaymentMicroserviceDirect();
  
  // Test 3: Customer Optimization (only if payment microservice works)
  if (results.paymentMicroservice.success) {
    results.customerOptimization = await testCustomerOptimization();
  }
  
  // Final Analysis
  console.log('\n📊 INTEGRATION TEST RESULTS:');
  console.log('=' .repeat(50));
  
  const flutterSuccess = results.flutterToSeekho.success;
  const paymentSuccess = results.paymentMicroservice.success;
  const optimizationSuccess = results.customerOptimization?.success || false;
  
  console.log(`📱 Flutter → Seekho-Backend: ${flutterSuccess ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`💳 Payment Microservice: ${paymentSuccess ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🚀 Customer Optimization: ${optimizationSuccess ? '✅ PASS' : '❌ FAIL'}`);
  
  if (flutterSuccess && paymentSuccess) {
    console.log('\n🎉 INTEGRATION SUCCESSFUL!');
    console.log('✅ End-to-end payment flow is working');
    console.log('✅ Customer creation fix is deployed and working');
    console.log('✅ Android app should now work perfectly');
    
    if (optimizationSuccess && results.customerOptimization.isOptimized) {
      console.log('🚀 Performance optimization is working');
      console.log(`   ~${results.customerOptimization.timeDifference}ms faster on repeat requests`);
    }
  } else {
    console.log('\n⚠️ INTEGRATION ISSUES DETECTED:');
    
    if (!flutterSuccess) {
      console.log('❌ Flutter → Seekho-Backend integration failing');
      console.log(`   Error: ${results.flutterToSeekho.error?.message || 'Unknown error'}`);
    }
    
    if (!paymentSuccess) {
      console.log('❌ Payment microservice integration failing');
      console.log(`   Error: ${results.paymentMicroservice.error?.error?.message || 'Unknown error'}`);
      console.log('🔧 Customer creation fix may not be deployed yet');
    }
  }
  
  console.log('\n📋 Next Steps:');
  if (flutterSuccess && paymentSuccess) {
    console.log('1. ✅ Test the Android app end-to-end');
    console.log('2. ✅ Verify payment completion in Razorpay dashboard');
    console.log('3. ✅ Monitor production metrics');
  } else {
    console.log('1. 🔧 Deploy the customer creation fix');
    console.log('2. 🔧 Re-run this integration test');
    console.log('3. 🔧 Check service logs for detailed errors');
  }
  
  return results;
}

// Run the integration tests
runIntegrationTests().catch(console.error);
