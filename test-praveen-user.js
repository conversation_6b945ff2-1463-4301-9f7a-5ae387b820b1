#!/usr/bin/env node

/**
 * Test Subscription Creation for Praveen User
 * 
 * This script tests subscription creation for the <NAME_EMAIL>
 * who has logged in 2 times and exists in the database.
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const PRODUCTION_BACKEND_URL = 'https://learner.netaapp.in';
const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u';

// Real user data from database
const PRAVEEN_USER = {
  _id: '688866c9baa54c07f2616aaf',
  packageId: 'com.gumbo.learning',
  name: '<PERSON><PERSON><PERSON>',
  email: '<EMAIL>',
  googleId: '101316776854821178055',
  provider: 'google',
  role: 'user',
  isVerified: true,
  isActive: true
};

console.log('🧪 Testing Subscription Creation for Praveen User');
console.log('=' .repeat(60));
console.log(`👤 User: ${PRAVEEN_USER.name} (${PRAVEEN_USER.email})`);
console.log(`🆔 User ID: ${PRAVEEN_USER._id}`);
console.log(`📦 Package ID: ${PRAVEEN_USER.packageId}`);

async function generateValidJWTForPraveen() {
  console.log('\n🔑 Step 1: Generating JWT token for Praveen...');
  
  const tokenPayload = {
    id: PRAVEEN_USER._id, // Use 'id' not 'userId' - this is what the backend expects!
    email: PRAVEEN_USER.email,
    name: PRAVEEN_USER.name,
    packageId: PRAVEEN_USER.packageId,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  const token = jwt.sign(tokenPayload, JWT_SECRET);
  
  console.log('✅ JWT token generated for Praveen');
  console.log('🔍 Token payload:', {
    id: tokenPayload.id, // Fixed: using 'id' instead of 'userId'
    email: tokenPayload.email,
    name: tokenPayload.name,
    packageId: tokenPayload.packageId,
    expiresIn: '24 hours'
  });
  
  return token;
}

async function testSubscriptionStatus(token) {
  console.log('\n📊 Step 2: Testing subscription status...');
  
  try {
    const response = await axios.get(
      `${PRODUCTION_BACKEND_URL}/api/subscriptions/status`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'x-package-id': PRAVEEN_USER.packageId,
        }
      }
    );
    
    console.log('✅ Subscription status check successful!');
    console.log('📋 Status:', response.data.subscriptionStatus);
    console.log('📋 Premium user:', response.data.premiumUser);
    console.log('📋 Previously purchased:', response.data.previouslyPurchased);
    
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ Subscription status check failed:');
    console.log('📡 Status:', error.response?.status);
    console.log('📋 Error:', error.response?.data?.message);
    
    return { success: false, error: error.response?.data };
  }
}

async function testSubscriptionCreation(token) {
  console.log('\n💳 Step 3: Testing subscription creation...');
  
  const requestBody = {
    plan: 'monthly',
    recurring: true,
    name: PRAVEEN_USER.name,
    email: PRAVEEN_USER.email,
    phone: '9876543210' // Default phone number
  };
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'x-package-id': PRAVEEN_USER.packageId,
  };
  
  console.log('📡 Making subscription creation request...');
  console.log('📋 Request body:', requestBody);
  
  try {
    const response = await axios.post(
      `${PRODUCTION_BACKEND_URL}/api/subscriptions/create-order`,
      requestBody,
      { headers }
    );
    
    console.log('✅ Subscription creation successful!');
    console.log('📡 Response status:', response.status);
    console.log('📋 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success && response.data.data) {
      console.log('\n🎉 Subscription Details:');
      console.log(`  Subscription ID: ${response.data.data.subscriptionId}`);
      console.log(`  Razorpay Subscription ID: ${response.data.data.razorpaySubscriptionId}`);
      console.log(`  Razorpay Customer ID: ${response.data.data.razorpayCustomerId}`);
      console.log(`  Amount: ₹${response.data.data.amount}`);
      console.log(`  Payment URL: ${response.data.data.shortUrl}`);
      console.log(`  Status: ${response.data.data.status}`);
    }
    
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ Subscription creation failed:');
    console.log('📡 Status:', error.response?.status);
    console.log('📋 Error:', JSON.stringify(error.response?.data, null, 2));
    
    // Analyze the specific error
    if (error.response?.status === 401) {
      console.log('🔍 401 Error Analysis:');
      console.log('  - User exists in database ✅');
      console.log('  - JWT token format is correct ✅');
      console.log('  - Issue might be: JWT secret mismatch');
      console.log('  - Or: Token payload structure mismatch');
    } else if (error.response?.status === 500) {
      console.log('🔍 500 Error Analysis:');
      console.log('  - Authentication likely worked ✅');
      console.log('  - Issue is in subscription creation logic');
      console.log('  - Check payment microservice integration');
    }
    
    return { success: false, error: error.response?.data };
  }
}

async function testYearlySubscription(token) {
  console.log('\n📅 Step 4: Testing yearly subscription creation...');
  
  const requestBody = {
    plan: 'yearly',
    recurring: true,
    name: PRAVEEN_USER.name,
    email: PRAVEEN_USER.email,
    phone: '9876543210'
  };
  
  try {
    const response = await axios.post(
      `${PRODUCTION_BACKEND_URL}/api/subscriptions/create-order`,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'x-package-id': PRAVEEN_USER.packageId,
        }
      }
    );
    
    console.log('✅ Yearly subscription creation successful!');
    console.log('📋 Yearly subscription ID:', response.data.data?.subscriptionId);
    
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ Yearly subscription creation failed:');
    console.log('📡 Status:', error.response?.status);
    console.log('📋 Error:', error.response?.data?.message);
    
    return { success: false, error: error.response?.data };
  }
}

async function runPraveenUserTest() {
  try {
    // Generate JWT token for the real user
    const token = await generateValidJWTForPraveen();
    
    // Test subscription status (should work if auth is correct)
    const statusResult = await testSubscriptionStatus(token);
    
    if (!statusResult.success) {
      console.log('\n❌ Authentication failed - cannot proceed with subscription tests');
      return;
    }
    
    // Test monthly subscription creation
    const monthlyResult = await testSubscriptionCreation(token);
    
    // Test yearly subscription creation
    const yearlyResult = await testYearlySubscription(token);
    
    console.log('\n📊 Test Results Summary:');
    console.log(`Subscription Status: ${statusResult.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Monthly Subscription: ${monthlyResult.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Yearly Subscription: ${yearlyResult.success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (monthlyResult.success && yearlyResult.success) {
      console.log('\n🎉 SUCCESS: Praveen user can create subscriptions!');
      console.log('💡 The Flutter app should work for this user');
      console.log('💡 If Flutter app still fails, the issue is in token storage/retrieval');
    } else {
      console.log('\n❌ FAILURE: Issues found with subscription creation');
      console.log('💡 The problem is in the backend subscription logic');
    }
    
  } catch (error) {
    console.error('💥 Test execution failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  runPraveenUserTest()
    .then(() => {
      console.log('\n🎯 Praveen user test completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { runPraveenUserTest };
