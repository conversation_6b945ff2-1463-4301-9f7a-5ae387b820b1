#!/usr/bin/env node

/**
 * Test Flutter Authentication Flow
 * 
 * This script simulates the complete Flutter app authentication and subscription flow
 * to identify where the authentication is breaking.
 */

const axios = require('axios');

const PRODUCTION_BACKEND_URL = 'https://learner.netaapp.in';

console.log('🔐 Testing Flutter Authentication Flow');
console.log('=' .repeat(60));

async function testGoogleAuth() {
  console.log('\n🔍 Step 1: Testing Google OAuth flow...');
  
  try {
    // Test the Google OAuth endpoint
    const response = await axios.get(`${PRODUCTION_BACKEND_URL}/api/auth/google`, {
      headers: {
        'x-package-id': 'com.gumbo.learning'
      }
    });
    
    console.log('✅ Google OAuth endpoint accessible');
    console.log('📋 Response:', response.status);
    
  } catch (error) {
    console.log('🔍 Google OAuth endpoint response:');
    console.log('  Status:', error.response?.status);
    console.log('  Headers:', error.response?.headers?.location ? 'Redirect to Google' : 'No redirect');
    
    if (error.response?.status === 302 || error.response?.headers?.location) {
      console.log('✅ Google OAuth redirect working (expected behavior)');
    }
  }
}

async function testUserProfile() {
  console.log('\n👤 Step 2: Testing user profile endpoint...');
  
  try {
    const response = await axios.get(`${PRODUCTION_BACKEND_URL}/api/users/me`, {
      headers: {
        'x-package-id': 'com.gumbo.learning'
        // Missing Authorization header - this should fail
      }
    });
    
    console.log('❌ Unexpected success - should require auth');
    
  } catch (error) {
    console.log('✅ User profile correctly requires authentication');
    console.log('  Status:', error.response?.status);
    console.log('  Message:', error.response?.data?.message);
  }
}

async function testSubscriptionWithoutAuth() {
  console.log('\n💳 Step 3: Testing subscription creation without auth...');
  
  try {
    const response = await axios.post(
      `${PRODUCTION_BACKEND_URL}/api/subscriptions/create-order`,
      {
        plan: 'monthly',
        recurring: true,
        phone: '9876543210'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-package-id': 'com.gumbo.learning'
          // Missing Authorization header
        }
      }
    );
    
    console.log('❌ Unexpected success - should require auth');
    
  } catch (error) {
    console.log('✅ Subscription correctly requires authentication');
    console.log('  Status:', error.response?.status);
    console.log('  Message:', error.response?.data?.message);
    
    if (error.response?.status === 401) {
      console.log('🔍 This matches the Flutter app error!');
    }
  }
}

async function generateTestToken() {
  console.log('\n🔑 Step 4: Generating test JWT token...');
  
  const jwt = require('jsonwebtoken');
  const JWT_SECRET = 'hdjdjkolso12339nfhf@1!u'; // From your .env file
  
  const testUser = {
    id: 'test_flutter_user_123',
    email: '<EMAIL>',
    name: 'Test Flutter User',
    packageId: 'com.gumbo.learning'
  };
  
  const token = jwt.sign(
    {
      userId: testUser.id,
      email: testUser.email,
      name: testUser.name,
      packageId: testUser.packageId
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
  
  console.log('✅ Test JWT token generated');
  console.log('🔍 Token payload:', {
    userId: testUser.id,
    email: testUser.email,
    packageId: testUser.packageId
  });
  
  return token;
}

async function testSubscriptionWithAuth(token) {
  console.log('\n💳 Step 5: Testing subscription creation WITH auth...');
  
  try {
    const response = await axios.post(
      `${PRODUCTION_BACKEND_URL}/api/subscriptions/create-order`,
      {
        plan: 'monthly',
        recurring: true,
        phone: '9876543210'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'x-package-id': 'com.gumbo.learning'
        }
      }
    );
    
    console.log('✅ Subscription created successfully with auth!');
    console.log('📋 Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ Subscription still failed with auth:');
    console.log('  Status:', error.response?.status);
    console.log('  Message:', error.response?.data?.message);
    
    if (error.response?.status === 500) {
      console.log('🔍 500 error suggests auth worked but subscription logic failed');
      console.log('🔍 This matches the Flutter app error pattern!');
    }
    
    // Log detailed error for debugging
    if (error.response?.data) {
      console.log('🔍 Detailed error:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function runAuthFlowTest() {
  try {
    await testGoogleAuth();
    await testUserProfile();
    await testSubscriptionWithoutAuth();
    
    const token = await generateTestToken();
    await testSubscriptionWithAuth(token);
    
    console.log('\n📊 Summary:');
    console.log('✅ Authentication endpoints are working');
    console.log('✅ JWT token generation is working');
    console.log('🔍 The issue is likely in the Flutter app\'s token handling');
    
    console.log('\n💡 Flutter App Debugging Steps:');
    console.log('1. Check if the Flutter app is storing JWT tokens correctly');
    console.log('2. Verify the Authorization header format in Flutter requests');
    console.log('3. Check if tokens are being included in subscription requests');
    console.log('4. Verify token expiration handling');
    console.log('5. Check Google OAuth callback handling in Flutter');
    
  } catch (error) {
    console.error('💥 Auth flow test failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  runAuthFlowTest()
    .then(() => {
      console.log('\n🎯 Auth flow test completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { runAuthFlowTest };
